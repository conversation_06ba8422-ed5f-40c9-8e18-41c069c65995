/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */(function() {'use strict';function n(e){throw e;}var p=void 0,aa=this;function t(e,b){var d=e.split("."),c=aa;!(d[0]in c)&&c.execScript&&c.execScript("var "+d[0]);for(var a;d.length&&(a=d.shift());)!d.length&&b!==p?c[a]=b:c=c[a]?c[a]:c[a]={}};var x="undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint16Array&&"undefined"!==typeof Uint32Array&&"undefined"!==typeof DataView;new (x?Uint8Array:Array)(256);var y;for(y=0;256>y;++y)for(var A=y,ba=7,A=A>>>1;A;A>>>=1)--ba;function B(e,b,d){var c,a="number"===typeof b?b:b=0,f="number"===typeof d?d:e.length;c=-1;for(a=f&7;a--;++b)c=c>>>8^C[(c^e[b])&255];for(a=f>>3;a--;b+=8)c=c>>>8^C[(c^e[b])&255],c=c>>>8^C[(c^e[b+1])&255],c=c>>>8^C[(c^e[b+2])&255],c=c>>>8^C[(c^e[b+3])&255],c=c>>>8^C[(c^e[b+4])&255],c=c>>>8^C[(c^e[b+5])&255],c=c>>>8^C[(c^e[b+6])&255],c=c>>>8^C[(c^e[b+7])&255];return(c^**********)>>>0}
var D=[0,**********,**********,**********,124634137,**********,**********,**********,249268274,**********,**********,**********,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,
2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,
2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,
2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,
3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,
936918E3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],C=x?new Uint32Array(D):D;function E(){}E.prototype.getName=function(){return this.name};E.prototype.getData=function(){return this.data};E.prototype.G=function(){return this.H};function G(e){var b=e.length,d=0,c=Number.POSITIVE_INFINITY,a,f,k,l,m,r,q,g,h,v;for(g=0;g<b;++g)e[g]>d&&(d=e[g]),e[g]<c&&(c=e[g]);a=1<<d;f=new (x?Uint32Array:Array)(a);k=1;l=0;for(m=2;k<=d;){for(g=0;g<b;++g)if(e[g]===k){r=0;q=l;for(h=0;h<k;++h)r=r<<1|q&1,q>>=1;v=k<<16|g;for(h=r;h<a;h+=m)f[h]=v;++l}++k;l<<=1;m<<=1}return[f,d,c]};var J=[],K;for(K=0;288>K;K++)switch(!0){case 143>=K:J.push([K+48,8]);break;case 255>=K:J.push([K-144+400,9]);break;case 279>=K:J.push([K-256+0,7]);break;case 287>=K:J.push([K-280+192,8]);break;default:n("invalid literal: "+K)}
var ca=function(){function e(a){switch(!0){case 3===a:return[257,a-3,0];case 4===a:return[258,a-4,0];case 5===a:return[259,a-5,0];case 6===a:return[260,a-6,0];case 7===a:return[261,a-7,0];case 8===a:return[262,a-8,0];case 9===a:return[263,a-9,0];case 10===a:return[264,a-10,0];case 12>=a:return[265,a-11,1];case 14>=a:return[266,a-13,1];case 16>=a:return[267,a-15,1];case 18>=a:return[268,a-17,1];case 22>=a:return[269,a-19,2];case 26>=a:return[270,a-23,2];case 30>=a:return[271,a-27,2];case 34>=a:return[272,
a-31,2];case 42>=a:return[273,a-35,3];case 50>=a:return[274,a-43,3];case 58>=a:return[275,a-51,3];case 66>=a:return[276,a-59,3];case 82>=a:return[277,a-67,4];case 98>=a:return[278,a-83,4];case 114>=a:return[279,a-99,4];case 130>=a:return[280,a-115,4];case 162>=a:return[281,a-131,5];case 194>=a:return[282,a-163,5];case 226>=a:return[283,a-195,5];case 257>=a:return[284,a-227,5];case 258===a:return[285,a-258,0];default:n("invalid length: "+a)}}var b=[],d,c;for(d=3;258>=d;d++)c=e(d),b[d]=c[2]<<24|c[1]<<
16|c[0];return b}();x&&new Uint32Array(ca);function L(e,b){this.i=[];this.j=32768;this.d=this.f=this.c=this.n=0;this.input=x?new Uint8Array(e):e;this.o=!1;this.k=M;this.w=!1;if(b||!(b={}))b.index&&(this.c=b.index),b.bufferSize&&(this.j=b.bufferSize),b.bufferType&&(this.k=b.bufferType),b.resize&&(this.w=b.resize);switch(this.k){case N:this.a=32768;this.b=new (x?Uint8Array:Array)(32768+this.j+258);break;case M:this.a=0;this.b=new (x?Uint8Array:Array)(this.j);this.e=this.D;this.q=this.A;this.l=this.C;break;default:n(Error("invalid inflate mode"))}}
var N=0,M=1;
L.prototype.g=function(){for(;!this.o;){var e=P(this,3);e&1&&(this.o=!0);e>>>=1;switch(e){case 0:var b=this.input,d=this.c,c=this.b,a=this.a,f=b.length,k=p,l=p,m=c.length,r=p;this.d=this.f=0;d+1>=f&&n(Error("invalid uncompressed block header: LEN"));k=b[d++]|b[d++]<<8;d+1>=f&&n(Error("invalid uncompressed block header: NLEN"));l=b[d++]|b[d++]<<8;k===~l&&n(Error("invalid uncompressed block header: length verify"));d+k>b.length&&n(Error("input buffer is broken"));switch(this.k){case N:for(;a+k>c.length;){r=
m-a;k-=r;if(x)c.set(b.subarray(d,d+r),a),a+=r,d+=r;else for(;r--;)c[a++]=b[d++];this.a=a;c=this.e();a=this.a}break;case M:for(;a+k>c.length;)c=this.e({t:2});break;default:n(Error("invalid inflate mode"))}if(x)c.set(b.subarray(d,d+k),a),a+=k,d+=k;else for(;k--;)c[a++]=b[d++];this.c=d;this.a=a;this.b=c;break;case 1:this.l(da,ea);break;case 2:for(var q=P(this,5)+257,g=P(this,5)+1,h=P(this,4)+4,v=new (x?Uint8Array:Array)(Q.length),s=p,F=p,H=p,w=p,z=p,O=p,I=p,u=p,Z=p,u=0;u<h;++u)v[Q[u]]=P(this,3);if(!x){u=
h;for(h=v.length;u<h;++u)v[Q[u]]=0}s=G(v);w=new (x?Uint8Array:Array)(q+g);u=0;for(Z=q+g;u<Z;)switch(z=R(this,s),z){case 16:for(I=3+P(this,2);I--;)w[u++]=O;break;case 17:for(I=3+P(this,3);I--;)w[u++]=0;O=0;break;case 18:for(I=11+P(this,7);I--;)w[u++]=0;O=0;break;default:O=w[u++]=z}F=x?G(w.subarray(0,q)):G(w.slice(0,q));H=x?G(w.subarray(q)):G(w.slice(q));this.l(F,H);break;default:n(Error("unknown BTYPE: "+e))}}return this.q()};
var S=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Q=x?new Uint16Array(S):S,fa=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],ga=x?new Uint16Array(fa):fa,ha=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],T=x?new Uint8Array(ha):ha,ia=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],ja=x?new Uint16Array(ia):ia,ka=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,
11,12,12,13,13],U=x?new Uint8Array(ka):ka,V=new (x?Uint8Array:Array)(288),W,la;W=0;for(la=V.length;W<la;++W)V[W]=143>=W?8:255>=W?9:279>=W?7:8;var da=G(V),X=new (x?Uint8Array:Array)(30),Y,ma;Y=0;for(ma=X.length;Y<ma;++Y)X[Y]=5;var ea=G(X);function P(e,b){for(var d=e.f,c=e.d,a=e.input,f=e.c,k=a.length,l;c<b;)f>=k&&n(Error("input buffer is broken")),d|=a[f++]<<c,c+=8;l=d&(1<<b)-1;e.f=d>>>b;e.d=c-b;e.c=f;return l}
function R(e,b){for(var d=e.f,c=e.d,a=e.input,f=e.c,k=a.length,l=b[0],m=b[1],r,q;c<m&&!(f>=k);)d|=a[f++]<<c,c+=8;r=l[d&(1<<m)-1];q=r>>>16;q>c&&n(Error("invalid code length: "+q));e.f=d>>q;e.d=c-q;e.c=f;return r&65535}
L.prototype.l=function(e,b){var d=this.b,c=this.a;this.r=e;for(var a=d.length-258,f,k,l,m;256!==(f=R(this,e));)if(256>f)c>=a&&(this.a=c,d=this.e(),c=this.a),d[c++]=f;else{k=f-257;m=ga[k];0<T[k]&&(m+=P(this,T[k]));f=R(this,b);l=ja[f];0<U[f]&&(l+=P(this,U[f]));c>=a&&(this.a=c,d=this.e(),c=this.a);for(;m--;)d[c]=d[c++-l]}for(;8<=this.d;)this.d-=8,this.c--;this.a=c};
L.prototype.C=function(e,b){var d=this.b,c=this.a;this.r=e;for(var a=d.length,f,k,l,m;256!==(f=R(this,e));)if(256>f)c>=a&&(d=this.e(),a=d.length),d[c++]=f;else{k=f-257;m=ga[k];0<T[k]&&(m+=P(this,T[k]));f=R(this,b);l=ja[f];0<U[f]&&(l+=P(this,U[f]));c+m>a&&(d=this.e(),a=d.length);for(;m--;)d[c]=d[c++-l]}for(;8<=this.d;)this.d-=8,this.c--;this.a=c};
L.prototype.e=function(){var e=new (x?Uint8Array:Array)(this.a-32768),b=this.a-32768,d,c,a=this.b;if(x)e.set(a.subarray(32768,e.length));else{d=0;for(c=e.length;d<c;++d)e[d]=a[d+32768]}this.i.push(e);this.n+=e.length;if(x)a.set(a.subarray(b,b+32768));else for(d=0;32768>d;++d)a[d]=a[b+d];this.a=32768;return a};
L.prototype.D=function(e){var b,d=this.input.length/this.c+1|0,c,a,f,k=this.input,l=this.b;e&&("number"===typeof e.t&&(d=e.t),"number"===typeof e.z&&(d+=e.z));2>d?(c=(k.length-this.c)/this.r[2],f=258*(c/2)|0,a=f<l.length?l.length+f:l.length<<1):a=l.length*d;x?(b=new Uint8Array(a),b.set(l)):b=l;return this.b=b};
L.prototype.q=function(){var e=0,b=this.b,d=this.i,c,a=new (x?Uint8Array:Array)(this.n+(this.a-32768)),f,k,l,m;if(0===d.length)return x?this.b.subarray(32768,this.a):this.b.slice(32768,this.a);f=0;for(k=d.length;f<k;++f){c=d[f];l=0;for(m=c.length;l<m;++l)a[e++]=c[l]}f=32768;for(k=this.a;f<k;++f)a[e++]=b[f];this.i=[];return this.buffer=a};
L.prototype.A=function(){var e,b=this.a;x?this.w?(e=new Uint8Array(b),e.set(this.b.subarray(0,b))):e=this.b.subarray(0,b):(this.b.length>b&&(this.b.length=b),e=this.b);return this.buffer=e};function $(e){this.input=e;this.c=0;this.m=[];this.s=!1}$.prototype.F=function(){this.s||this.g();return this.m.slice()};
$.prototype.g=function(){for(var e=this.input.length;this.c<e;){var b=new E,d=p,c=p,a=p,f=p,k=p,l=p,m=p,r=p,q=p,g=this.input,h=this.c;b.u=g[h++];b.v=g[h++];(31!==b.u||139!==b.v)&&n(Error("invalid file signature:"+b.u+","+b.v));b.p=g[h++];switch(b.p){case 8:break;default:n(Error("unknown compression method: "+b.p))}b.h=g[h++];r=g[h++]|g[h++]<<8|g[h++]<<16|g[h++]<<24;b.H=new Date(1E3*r);b.N=g[h++];b.M=g[h++];0<(b.h&4)&&(b.I=g[h++]|g[h++]<<8,h+=b.I);if(0<(b.h&8)){m=[];for(l=0;0<(k=g[h++]);)m[l++]=String.fromCharCode(k);
b.name=m.join("")}if(0<(b.h&16)){m=[];for(l=0;0<(k=g[h++]);)m[l++]=String.fromCharCode(k);b.J=m.join("")}0<(b.h&2)&&(b.B=B(g,0,h)&65535,b.B!==(g[h++]|g[h++]<<8)&&n(Error("invalid header crc16")));d=g[g.length-4]|g[g.length-3]<<8|g[g.length-2]<<16|g[g.length-1]<<24;g.length-h-4-4<512*d&&(f=d);c=new L(g,{index:h,bufferSize:f});b.data=a=c.g();h=c.c;b.K=q=(g[h++]|g[h++]<<8|g[h++]<<16|g[h++]<<24)>>>0;B(a,p,p)!==q&&n(Error("invalid CRC-32 checksum: 0x"+B(a,p,p).toString(16)+" / 0x"+q.toString(16)));b.L=
d=(g[h++]|g[h++]<<8|g[h++]<<16|g[h++]<<24)>>>0;(a.length&**********)!==d&&n(Error("invalid input size: "+(a.length&**********)+" / "+d));this.m.push(b);this.c=h}this.s=!0;var v=this.m,s,F,H=0,w=0,z;s=0;for(F=v.length;s<F;++s)w+=v[s].data.length;if(x){z=new Uint8Array(w);for(s=0;s<F;++s)z.set(v[s].data,H),H+=v[s].data.length}else{z=[];for(s=0;s<F;++s)z[s]=v[s].data;z=Array.prototype.concat.apply([],z)}return z};t("Zlib.Gunzip",$);t("Zlib.Gunzip.prototype.decompress",$.prototype.g);t("Zlib.Gunzip.prototype.getMembers",$.prototype.F);t("Zlib.GunzipMember",E);t("Zlib.GunzipMember.prototype.getName",E.prototype.getName);t("Zlib.GunzipMember.prototype.getData",E.prototype.getData);t("Zlib.GunzipMember.prototype.getMtime",E.prototype.G);}).call(this);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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