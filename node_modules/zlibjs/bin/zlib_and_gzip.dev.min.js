/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */(function() {'use strict';function q(d){throw d;}var t=void 0,v=!0,ca=this;function B(d,a){var c=d.split("."),b=ca;!(c[0]in b)&&b.execScript&&b.execScript("var "+c[0]);for(var e;c.length&&(e=c.shift());)!c.length&&a!==t?b[e]=a:b=b[e]?b[e]:b[e]={}};var E="undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint16Array&&"undefined"!==typeof Uint32Array&&"undefined"!==typeof DataView;function H(d,a){this.index="number"===typeof a?a:0;this.m=0;this.buffer=d instanceof(E?Uint8Array:Array)?d:new (E?Uint8Array:Array)(32768);2*this.buffer.length<=this.index&&q(Error("invalid index"));this.buffer.length<=this.index&&this.f()}H.prototype.f=function(){var d=this.buffer,a,c=d.length,b=new (E?Uint8Array:Array)(c<<1);if(E)b.set(d);else for(a=0;a<c;++a)b[a]=d[a];return this.buffer=b};
H.prototype.d=function(d,a,c){var b=this.buffer,e=this.index,f=this.m,g=b[e],k;c&&1<a&&(d=8<a?(J[d&255]<<24|J[d>>>8&255]<<16|J[d>>>16&255]<<8|J[d>>>24&255])>>32-a:J[d]>>8-a);if(8>a+f)g=g<<a|d,f+=a;else for(k=0;k<a;++k)g=g<<1|d>>a-k-1&1,8===++f&&(f=0,b[e++]=J[g],g=0,e===b.length&&(b=this.f()));b[e]=g;this.buffer=b;this.m=f;this.index=e};H.prototype.finish=function(){var d=this.buffer,a=this.index,c;0<this.m&&(d[a]<<=8-this.m,d[a]=J[d[a]],a++);E?c=d.subarray(0,a):(d.length=a,c=d);return c};
var da=new (E?Uint8Array:Array)(256),ea;for(ea=0;256>ea;++ea){for(var M=ea,fa=M,ka=7,M=M>>>1;M;M>>>=1)fa<<=1,fa|=M&1,--ka;da[ea]=(fa<<ka&255)>>>0}var J=da;function la(d,a,c){var b,e="number"===typeof a?a:a=0,f="number"===typeof c?c:d.length;b=-1;for(e=f&7;e--;++a)b=b>>>8^S[(b^d[a])&255];for(e=f>>3;e--;a+=8)b=b>>>8^S[(b^d[a])&255],b=b>>>8^S[(b^d[a+1])&255],b=b>>>8^S[(b^d[a+2])&255],b=b>>>8^S[(b^d[a+3])&255],b=b>>>8^S[(b^d[a+4])&255],b=b>>>8^S[(b^d[a+5])&255],b=b>>>8^S[(b^d[a+6])&255],b=b>>>8^S[(b^d[a+7])&255];return(b^4294967295)>>>0}
var ma=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,
2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,
2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,
2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,
3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,
936918E3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],S=E?new Uint32Array(ma):ma;function T(){}T.prototype.getName=function(){return this.name};T.prototype.getData=function(){return this.data};T.prototype.X=function(){return this.Y};function na(d){this.buffer=new (E?Uint16Array:Array)(2*d);this.length=0}na.prototype.getParent=function(d){return 2*((d-2)/4|0)};na.prototype.push=function(d,a){var c,b,e=this.buffer,f;c=this.length;e[this.length++]=a;for(e[this.length++]=d;0<c;)if(b=this.getParent(c),e[c]>e[b])f=e[c],e[c]=e[b],e[b]=f,f=e[c+1],e[c+1]=e[b+1],e[b+1]=f,c=b;else break;return this.length};
na.prototype.pop=function(){var d,a,c=this.buffer,b,e,f;a=c[0];d=c[1];this.length-=2;c[0]=c[this.length];c[1]=c[this.length+1];for(f=0;;){e=2*f+2;if(e>=this.length)break;e+2<this.length&&c[e+2]>c[e]&&(e+=2);if(c[e]>c[f])b=c[f],c[f]=c[e],c[e]=b,b=c[f+1],c[f+1]=c[e+1],c[e+1]=b;else break;f=e}return{index:d,value:a,length:this.length}};function U(d){var a=d.length,c=0,b=Number.POSITIVE_INFINITY,e,f,g,k,h,m,r,p,l,n;for(p=0;p<a;++p)d[p]>c&&(c=d[p]),d[p]<b&&(b=d[p]);e=1<<c;f=new (E?Uint32Array:Array)(e);g=1;k=0;for(h=2;g<=c;){for(p=0;p<a;++p)if(d[p]===g){m=0;r=k;for(l=0;l<g;++l)m=m<<1|r&1,r>>=1;n=g<<16|p;for(l=m;l<e;l+=h)f[l]=n;++k}++g;k<<=1;h<<=1}return[f,c,b]};function oa(d,a){this.k=ra;this.I=0;this.input=E&&d instanceof Array?new Uint8Array(d):d;this.b=0;a&&(a.lazy&&(this.I=a.lazy),"number"===typeof a.compressionType&&(this.k=a.compressionType),a.outputBuffer&&(this.a=E&&a.outputBuffer instanceof Array?new Uint8Array(a.outputBuffer):a.outputBuffer),"number"===typeof a.outputIndex&&(this.b=a.outputIndex));this.a||(this.a=new (E?Uint8Array:Array)(32768))}var ra=2,sa={NONE:0,v:1,o:ra,aa:3},ta=[],V;
for(V=0;288>V;V++)switch(v){case 143>=V:ta.push([V+48,8]);break;case 255>=V:ta.push([V-144+400,9]);break;case 279>=V:ta.push([V-256+0,7]);break;case 287>=V:ta.push([V-280+192,8]);break;default:q("invalid literal: "+V)}
oa.prototype.g=function(){var d,a,c,b,e=this.input;switch(this.k){case 0:c=0;for(b=e.length;c<b;){a=E?e.subarray(c,c+65535):e.slice(c,c+65535);c+=a.length;var f=a,g=c===b,k=t,h=t,m=t,r=t,p=t,l=this.a,n=this.b;if(E){for(l=new Uint8Array(this.a.buffer);l.length<=n+f.length+5;)l=new Uint8Array(l.length<<1);l.set(this.a)}k=g?1:0;l[n++]=k|0;h=f.length;m=~h+65536&65535;l[n++]=h&255;l[n++]=h>>>8&255;l[n++]=m&255;l[n++]=m>>>8&255;if(E)l.set(f,n),n+=f.length,l=l.subarray(0,n);else{r=0;for(p=f.length;r<p;++r)l[n++]=
f[r];l.length=n}this.b=n;this.a=l}break;case 1:var s=new H(E?new Uint8Array(this.a.buffer):this.a,this.b);s.d(1,1,v);s.d(1,2,v);var u=ua(this,e),w,C,x;w=0;for(C=u.length;w<C;w++)if(x=u[w],H.prototype.d.apply(s,ta[x]),256<x)s.d(u[++w],u[++w],v),s.d(u[++w],5),s.d(u[++w],u[++w],v);else if(256===x)break;this.a=s.finish();this.b=this.a.length;break;case ra:var D=new H(E?new Uint8Array(this.a.buffer):this.a,this.b),N,z,O,$,aa,pb=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],ga,La,ha,Ma,pa,xa=Array(19),
Na,ba,qa,F,Oa;N=ra;D.d(1,1,v);D.d(N,2,v);z=ua(this,e);ga=va(this.V,15);La=wa(ga);ha=va(this.U,7);Ma=wa(ha);for(O=286;257<O&&0===ga[O-1];O--);for($=30;1<$&&0===ha[$-1];$--);var Pa=O,Qa=$,L=new (E?Uint32Array:Array)(Pa+Qa),y,P,A,ia,K=new (E?Uint32Array:Array)(316),I,G,Q=new (E?Uint8Array:Array)(19);for(y=P=0;y<Pa;y++)L[P++]=ga[y];for(y=0;y<Qa;y++)L[P++]=ha[y];if(!E){y=0;for(ia=Q.length;y<ia;++y)Q[y]=0}y=I=0;for(ia=L.length;y<ia;y+=P){for(P=1;y+P<ia&&L[y+P]===L[y];++P);A=P;if(0===L[y])if(3>A)for(;0<
A--;)K[I++]=0,Q[0]++;else for(;0<A;)G=138>A?A:138,G>A-3&&G<A&&(G=A-3),10>=G?(K[I++]=17,K[I++]=G-3,Q[17]++):(K[I++]=18,K[I++]=G-11,Q[18]++),A-=G;else if(K[I++]=L[y],Q[L[y]]++,A--,3>A)for(;0<A--;)K[I++]=L[y],Q[L[y]]++;else for(;0<A;)G=6>A?A:6,G>A-3&&G<A&&(G=A-3),K[I++]=16,K[I++]=G-3,Q[16]++,A-=G}d=E?K.subarray(0,I):K.slice(0,I);pa=va(Q,7);for(F=0;19>F;F++)xa[F]=pa[pb[F]];for(aa=19;4<aa&&0===xa[aa-1];aa--);Na=wa(pa);D.d(O-257,5,v);D.d($-1,5,v);D.d(aa-4,4,v);for(F=0;F<aa;F++)D.d(xa[F],3,v);F=0;for(Oa=
d.length;F<Oa;F++)if(ba=d[F],D.d(Na[ba],pa[ba],v),16<=ba){F++;switch(ba){case 16:qa=2;break;case 17:qa=3;break;case 18:qa=7;break;default:q("invalid code: "+ba)}D.d(d[F],qa,v)}var Ra=[La,ga],Sa=[Ma,ha],R,Ta,ja,Aa,Ua,Va,Wa,Xa;Ua=Ra[0];Va=Ra[1];Wa=Sa[0];Xa=Sa[1];R=0;for(Ta=z.length;R<Ta;++R)if(ja=z[R],D.d(Ua[ja],Va[ja],v),256<ja)D.d(z[++R],z[++R],v),Aa=z[++R],D.d(Wa[Aa],Xa[Aa],v),D.d(z[++R],z[++R],v);else if(256===ja)break;this.a=D.finish();this.b=this.a.length;break;default:q("invalid compression type")}return this.a};
function ya(d,a){this.length=d;this.P=a}
var za=function(){function d(a){switch(v){case 3===a:return[257,a-3,0];case 4===a:return[258,a-4,0];case 5===a:return[259,a-5,0];case 6===a:return[260,a-6,0];case 7===a:return[261,a-7,0];case 8===a:return[262,a-8,0];case 9===a:return[263,a-9,0];case 10===a:return[264,a-10,0];case 12>=a:return[265,a-11,1];case 14>=a:return[266,a-13,1];case 16>=a:return[267,a-15,1];case 18>=a:return[268,a-17,1];case 22>=a:return[269,a-19,2];case 26>=a:return[270,a-23,2];case 30>=a:return[271,a-27,2];case 34>=a:return[272,
a-31,2];case 42>=a:return[273,a-35,3];case 50>=a:return[274,a-43,3];case 58>=a:return[275,a-51,3];case 66>=a:return[276,a-59,3];case 82>=a:return[277,a-67,4];case 98>=a:return[278,a-83,4];case 114>=a:return[279,a-99,4];case 130>=a:return[280,a-115,4];case 162>=a:return[281,a-131,5];case 194>=a:return[282,a-163,5];case 226>=a:return[283,a-195,5];case 257>=a:return[284,a-227,5];case 258===a:return[285,a-258,0];default:q("invalid length: "+a)}}var a=[],c,b;for(c=3;258>=c;c++)b=d(c),a[c]=b[2]<<24|b[1]<<
16|b[0];return a}(),Ba=E?new Uint32Array(za):za;
function ua(d,a){function c(a,c){var b=a.P,d=[],e=0,f;f=Ba[a.length];d[e++]=f&65535;d[e++]=f>>16&255;d[e++]=f>>24;var g;switch(v){case 1===b:g=[0,b-1,0];break;case 2===b:g=[1,b-2,0];break;case 3===b:g=[2,b-3,0];break;case 4===b:g=[3,b-4,0];break;case 6>=b:g=[4,b-5,1];break;case 8>=b:g=[5,b-7,1];break;case 12>=b:g=[6,b-9,2];break;case 16>=b:g=[7,b-13,2];break;case 24>=b:g=[8,b-17,3];break;case 32>=b:g=[9,b-25,3];break;case 48>=b:g=[10,b-33,4];break;case 64>=b:g=[11,b-49,4];break;case 96>=b:g=[12,b-
65,5];break;case 128>=b:g=[13,b-97,5];break;case 192>=b:g=[14,b-129,6];break;case 256>=b:g=[15,b-193,6];break;case 384>=b:g=[16,b-257,7];break;case 512>=b:g=[17,b-385,7];break;case 768>=b:g=[18,b-513,8];break;case 1024>=b:g=[19,b-769,8];break;case 1536>=b:g=[20,b-1025,9];break;case 2048>=b:g=[21,b-1537,9];break;case 3072>=b:g=[22,b-2049,10];break;case 4096>=b:g=[23,b-3073,10];break;case 6144>=b:g=[24,b-4097,11];break;case 8192>=b:g=[25,b-6145,11];break;case 12288>=b:g=[26,b-8193,12];break;case 16384>=
b:g=[27,b-12289,12];break;case 24576>=b:g=[28,b-16385,13];break;case 32768>=b:g=[29,b-24577,13];break;default:q("invalid distance")}f=g;d[e++]=f[0];d[e++]=f[1];d[e++]=f[2];var h,k;h=0;for(k=d.length;h<k;++h)l[n++]=d[h];u[d[0]]++;w[d[3]]++;s=a.length+c-1;p=null}var b,e,f,g,k,h={},m,r,p,l=E?new Uint16Array(2*a.length):[],n=0,s=0,u=new (E?Uint32Array:Array)(286),w=new (E?Uint32Array:Array)(30),C=d.I,x;if(!E){for(f=0;285>=f;)u[f++]=0;for(f=0;29>=f;)w[f++]=0}u[256]=1;b=0;for(e=a.length;b<e;++b){f=k=0;
for(g=3;f<g&&b+f!==e;++f)k=k<<8|a[b+f];h[k]===t&&(h[k]=[]);m=h[k];if(!(0<s--)){for(;0<m.length&&32768<b-m[0];)m.shift();if(b+3>=e){p&&c(p,-1);f=0;for(g=e-b;f<g;++f)x=a[b+f],l[n++]=x,++u[x];break}0<m.length?(r=Ca(a,b,m),p?p.length<r.length?(x=a[b-1],l[n++]=x,++u[x],c(r,0)):c(p,-1):r.length<C?p=r:c(r,0)):p?c(p,-1):(x=a[b],l[n++]=x,++u[x])}m.push(b)}l[n++]=256;u[256]++;d.V=u;d.U=w;return E?l.subarray(0,n):l}
function Ca(d,a,c){var b,e,f=0,g,k,h,m,r=d.length;k=0;m=c.length;a:for(;k<m;k++){b=c[m-k-1];g=3;if(3<f){for(h=f;3<h;h--)if(d[b+h-1]!==d[a+h-1])continue a;g=f}for(;258>g&&a+g<r&&d[b+g]===d[a+g];)++g;g>f&&(e=b,f=g);if(258===g)break}return new ya(f,a-e)}
function va(d,a){var c=d.length,b=new na(572),e=new (E?Uint8Array:Array)(c),f,g,k,h,m;if(!E)for(h=0;h<c;h++)e[h]=0;for(h=0;h<c;++h)0<d[h]&&b.push(h,d[h]);f=Array(b.length/2);g=new (E?Uint32Array:Array)(b.length/2);if(1===f.length)return e[b.pop().index]=1,e;h=0;for(m=b.length/2;h<m;++h)f[h]=b.pop(),g[h]=f[h].value;k=Da(g,g.length,a);h=0;for(m=f.length;h<m;++h)e[f[h].index]=k[h];return e}
function Da(d,a,c){function b(c){var d=h[c][m[c]];d===a?(b(c+1),b(c+1)):--g[d];++m[c]}var e=new (E?Uint16Array:Array)(c),f=new (E?Uint8Array:Array)(c),g=new (E?Uint8Array:Array)(a),k=Array(c),h=Array(c),m=Array(c),r=(1<<c)-a,p=1<<c-1,l,n,s,u,w;e[c-1]=a;for(n=0;n<c;++n)r<p?f[n]=0:(f[n]=1,r-=p),r<<=1,e[c-2-n]=(e[c-1-n]/2|0)+a;e[0]=f[0];k[0]=Array(e[0]);h[0]=Array(e[0]);for(n=1;n<c;++n)e[n]>2*e[n-1]+f[n]&&(e[n]=2*e[n-1]+f[n]),k[n]=Array(e[n]),h[n]=Array(e[n]);for(l=0;l<a;++l)g[l]=c;for(s=0;s<e[c-1];++s)k[c-
1][s]=d[s],h[c-1][s]=s;for(l=0;l<c;++l)m[l]=0;1===f[c-1]&&(--g[0],++m[c-1]);for(n=c-2;0<=n;--n){u=l=0;w=m[n+1];for(s=0;s<e[n];s++)u=k[n+1][w]+k[n+1][w+1],u>d[l]?(k[n][s]=u,h[n][s]=a,w+=2):(k[n][s]=d[l],h[n][s]=l,++l);m[n]=0;1===f[n]&&b(n)}return g}
function wa(d){var a=new (E?Uint16Array:Array)(d.length),c=[],b=[],e=0,f,g,k,h;f=0;for(g=d.length;f<g;f++)c[d[f]]=(c[d[f]]|0)+1;f=1;for(g=16;f<=g;f++)b[f]=e,e+=c[f]|0,e<<=1;f=0;for(g=d.length;f<g;f++){e=b[d[f]];b[d[f]]+=1;k=a[f]=0;for(h=d[f];k<h;k++)a[f]=a[f]<<1|e&1,e>>>=1}return a};function Ea(d,a){this.input=d;this.b=this.c=0;this.i={};a&&(a.flags&&(this.i=a.flags),"string"===typeof a.filename&&(this.filename=a.filename),"string"===typeof a.comment&&(this.A=a.comment),a.deflateOptions&&(this.l=a.deflateOptions));this.l||(this.l={})}
Ea.prototype.g=function(){var d,a,c,b,e,f,g,k,h=new (E?Uint8Array:Array)(32768),m=0,r=this.input,p=this.c,l=this.filename,n=this.A;h[m++]=31;h[m++]=139;h[m++]=8;d=0;this.i.fname&&(d|=Fa);this.i.fcomment&&(d|=Ga);this.i.fhcrc&&(d|=Ha);h[m++]=d;a=(Date.now?Date.now():+new Date)/1E3|0;h[m++]=a&255;h[m++]=a>>>8&255;h[m++]=a>>>16&255;h[m++]=a>>>24&255;h[m++]=0;h[m++]=Ia;if(this.i.fname!==t){g=0;for(k=l.length;g<k;++g)f=l.charCodeAt(g),255<f&&(h[m++]=f>>>8&255),h[m++]=f&255;h[m++]=0}if(this.i.comment){g=
0;for(k=n.length;g<k;++g)f=n.charCodeAt(g),255<f&&(h[m++]=f>>>8&255),h[m++]=f&255;h[m++]=0}this.i.fhcrc&&(c=la(h,0,m)&65535,h[m++]=c&255,h[m++]=c>>>8&255);this.l.outputBuffer=h;this.l.outputIndex=m;e=new oa(r,this.l);h=e.g();m=e.b;E&&(m+8>h.buffer.byteLength?(this.a=new Uint8Array(m+8),this.a.set(new Uint8Array(h.buffer)),h=this.a):h=new Uint8Array(h.buffer));b=la(r,t,t);h[m++]=b&255;h[m++]=b>>>8&255;h[m++]=b>>>16&255;h[m++]=b>>>24&255;k=r.length;h[m++]=k&255;h[m++]=k>>>8&255;h[m++]=k>>>16&255;h[m++]=
k>>>24&255;this.c=p;E&&m<h.length&&(this.a=h=h.subarray(0,m));return h};var Ia=255,Ha=2,Fa=8,Ga=16;function W(d,a){this.p=[];this.q=32768;this.e=this.j=this.c=this.u=0;this.input=E?new Uint8Array(d):d;this.w=!1;this.r=Ja;this.L=!1;if(a||!(a={}))a.index&&(this.c=a.index),a.bufferSize&&(this.q=a.bufferSize),a.bufferType&&(this.r=a.bufferType),a.resize&&(this.L=a.resize);switch(this.r){case Ka:this.b=32768;this.a=new (E?Uint8Array:Array)(32768+this.q+258);break;case Ja:this.b=0;this.a=new (E?Uint8Array:Array)(this.q);this.f=this.T;this.B=this.Q;this.s=this.S;break;default:q(Error("invalid inflate mode"))}}
var Ka=0,Ja=1,Ya={N:Ka,M:Ja};
W.prototype.h=function(){for(;!this.w;){var d=X(this,3);d&1&&(this.w=v);d>>>=1;switch(d){case 0:var a=this.input,c=this.c,b=this.a,e=this.b,f=a.length,g=t,k=t,h=b.length,m=t;this.e=this.j=0;c+1>=f&&q(Error("invalid uncompressed block header: LEN"));g=a[c++]|a[c++]<<8;c+1>=f&&q(Error("invalid uncompressed block header: NLEN"));k=a[c++]|a[c++]<<8;g===~k&&q(Error("invalid uncompressed block header: length verify"));c+g>a.length&&q(Error("input buffer is broken"));switch(this.r){case Ka:for(;e+g>b.length;){m=
h-e;g-=m;if(E)b.set(a.subarray(c,c+m),e),e+=m,c+=m;else for(;m--;)b[e++]=a[c++];this.b=e;b=this.f();e=this.b}break;case Ja:for(;e+g>b.length;)b=this.f({F:2});break;default:q(Error("invalid inflate mode"))}if(E)b.set(a.subarray(c,c+g),e),e+=g,c+=g;else for(;g--;)b[e++]=a[c++];this.c=c;this.b=e;this.a=b;break;case 1:this.s(Za,$a);break;case 2:for(var r=X(this,5)+257,p=X(this,5)+1,l=X(this,4)+4,n=new (E?Uint8Array:Array)(ab.length),s=t,u=t,w=t,C=t,x=t,D=t,N=t,z=t,O=t,z=0;z<l;++z)n[ab[z]]=X(this,3);if(!E){z=
l;for(l=n.length;z<l;++z)n[ab[z]]=0}s=U(n);C=new (E?Uint8Array:Array)(r+p);z=0;for(O=r+p;z<O;)switch(x=bb(this,s),x){case 16:for(N=3+X(this,2);N--;)C[z++]=D;break;case 17:for(N=3+X(this,3);N--;)C[z++]=0;D=0;break;case 18:for(N=11+X(this,7);N--;)C[z++]=0;D=0;break;default:D=C[z++]=x}u=E?U(C.subarray(0,r)):U(C.slice(0,r));w=E?U(C.subarray(r)):U(C.slice(r));this.s(u,w);break;default:q(Error("unknown BTYPE: "+d))}}return this.B()};
var cb=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],ab=E?new Uint16Array(cb):cb,db=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],eb=E?new Uint16Array(db):db,fb=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],gb=E?new Uint8Array(fb):fb,hb=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],ib=E?new Uint16Array(hb):hb,jb=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,
10,11,11,12,12,13,13],kb=E?new Uint8Array(jb):jb,lb=new (E?Uint8Array:Array)(288),Y,mb;Y=0;for(mb=lb.length;Y<mb;++Y)lb[Y]=143>=Y?8:255>=Y?9:279>=Y?7:8;var Za=U(lb),nb=new (E?Uint8Array:Array)(30),ob,qb;ob=0;for(qb=nb.length;ob<qb;++ob)nb[ob]=5;var $a=U(nb);function X(d,a){for(var c=d.j,b=d.e,e=d.input,f=d.c,g=e.length,k;b<a;)f>=g&&q(Error("input buffer is broken")),c|=e[f++]<<b,b+=8;k=c&(1<<a)-1;d.j=c>>>a;d.e=b-a;d.c=f;return k}
function bb(d,a){for(var c=d.j,b=d.e,e=d.input,f=d.c,g=e.length,k=a[0],h=a[1],m,r;b<h&&!(f>=g);)c|=e[f++]<<b,b+=8;m=k[c&(1<<h)-1];r=m>>>16;r>b&&q(Error("invalid code length: "+r));d.j=c>>r;d.e=b-r;d.c=f;return m&65535}
W.prototype.s=function(d,a){var c=this.a,b=this.b;this.C=d;for(var e=c.length-258,f,g,k,h;256!==(f=bb(this,d));)if(256>f)b>=e&&(this.b=b,c=this.f(),b=this.b),c[b++]=f;else{g=f-257;h=eb[g];0<gb[g]&&(h+=X(this,gb[g]));f=bb(this,a);k=ib[f];0<kb[f]&&(k+=X(this,kb[f]));b>=e&&(this.b=b,c=this.f(),b=this.b);for(;h--;)c[b]=c[b++-k]}for(;8<=this.e;)this.e-=8,this.c--;this.b=b};
W.prototype.S=function(d,a){var c=this.a,b=this.b;this.C=d;for(var e=c.length,f,g,k,h;256!==(f=bb(this,d));)if(256>f)b>=e&&(c=this.f(),e=c.length),c[b++]=f;else{g=f-257;h=eb[g];0<gb[g]&&(h+=X(this,gb[g]));f=bb(this,a);k=ib[f];0<kb[f]&&(k+=X(this,kb[f]));b+h>e&&(c=this.f(),e=c.length);for(;h--;)c[b]=c[b++-k]}for(;8<=this.e;)this.e-=8,this.c--;this.b=b};
W.prototype.f=function(){var d=new (E?Uint8Array:Array)(this.b-32768),a=this.b-32768,c,b,e=this.a;if(E)d.set(e.subarray(32768,d.length));else{c=0;for(b=d.length;c<b;++c)d[c]=e[c+32768]}this.p.push(d);this.u+=d.length;if(E)e.set(e.subarray(a,a+32768));else for(c=0;32768>c;++c)e[c]=e[a+c];this.b=32768;return e};
W.prototype.T=function(d){var a,c=this.input.length/this.c+1|0,b,e,f,g=this.input,k=this.a;d&&("number"===typeof d.F&&(c=d.F),"number"===typeof d.O&&(c+=d.O));2>c?(b=(g.length-this.c)/this.C[2],f=258*(b/2)|0,e=f<k.length?k.length+f:k.length<<1):e=k.length*c;E?(a=new Uint8Array(e),a.set(k)):a=k;return this.a=a};
W.prototype.B=function(){var d=0,a=this.a,c=this.p,b,e=new (E?Uint8Array:Array)(this.u+(this.b-32768)),f,g,k,h;if(0===c.length)return E?this.a.subarray(32768,this.b):this.a.slice(32768,this.b);f=0;for(g=c.length;f<g;++f){b=c[f];k=0;for(h=b.length;k<h;++k)e[d++]=b[k]}f=32768;for(g=this.b;f<g;++f)e[d++]=a[f];this.p=[];return this.buffer=e};
W.prototype.Q=function(){var d,a=this.b;E?this.L?(d=new Uint8Array(a),d.set(this.a.subarray(0,a))):d=this.a.subarray(0,a):(this.a.length>a&&(this.a.length=a),d=this.a);return this.buffer=d};function rb(d){this.input=d;this.c=0;this.t=[];this.D=!1}rb.prototype.W=function(){this.D||this.h();return this.t.slice()};
rb.prototype.h=function(){for(var d=this.input.length;this.c<d;){var a=new T,c=t,b=t,e=t,f=t,g=t,k=t,h=t,m=t,r=t,p=this.input,l=this.c;a.G=p[l++];a.H=p[l++];(31!==a.G||139!==a.H)&&q(Error("invalid file signature:"+a.G+","+a.H));a.z=p[l++];switch(a.z){case 8:break;default:q(Error("unknown compression method: "+a.z))}a.n=p[l++];m=p[l++]|p[l++]<<8|p[l++]<<16|p[l++]<<24;a.Y=new Date(1E3*m);a.ea=p[l++];a.da=p[l++];0<(a.n&4)&&(a.$=p[l++]|p[l++]<<8,l+=a.$);if(0<(a.n&Fa)){h=[];for(k=0;0<(g=p[l++]);)h[k++]=
String.fromCharCode(g);a.name=h.join("")}if(0<(a.n&Ga)){h=[];for(k=0;0<(g=p[l++]);)h[k++]=String.fromCharCode(g);a.A=h.join("")}0<(a.n&Ha)&&(a.R=la(p,0,l)&65535,a.R!==(p[l++]|p[l++]<<8)&&q(Error("invalid header crc16")));c=p[p.length-4]|p[p.length-3]<<8|p[p.length-2]<<16|p[p.length-1]<<24;p.length-l-4-4<512*c&&(f=c);b=new W(p,{index:l,bufferSize:f});a.data=e=b.h();l=b.c;a.ba=r=(p[l++]|p[l++]<<8|p[l++]<<16|p[l++]<<24)>>>0;la(e,t,t)!==r&&q(Error("invalid CRC-32 checksum: 0x"+la(e,t,t).toString(16)+
" / 0x"+r.toString(16)));a.ca=c=(p[l++]|p[l++]<<8|p[l++]<<16|p[l++]<<24)>>>0;(e.length&4294967295)!==c&&q(Error("invalid input size: "+(e.length&4294967295)+" / "+c));this.t.push(a);this.c=l}this.D=v;var n=this.t,s,u,w=0,C=0,x;s=0;for(u=n.length;s<u;++s)C+=n[s].data.length;if(E){x=new Uint8Array(C);for(s=0;s<u;++s)x.set(n[s].data,w),w+=n[s].data.length}else{x=[];for(s=0;s<u;++s)x[s]=n[s].data;x=Array.prototype.concat.apply([],x)}return x};function sb(d){if("string"===typeof d){var a=d.split(""),c,b;c=0;for(b=a.length;c<b;c++)a[c]=(a[c].charCodeAt(0)&255)>>>0;d=a}for(var e=1,f=0,g=d.length,k,h=0;0<g;){k=1024<g?1024:g;g-=k;do e+=d[h++],f+=e;while(--k);e%=65521;f%=65521}return(f<<16|e)>>>0};function tb(d,a){var c,b;this.input=d;this.c=0;if(a||!(a={}))a.index&&(this.c=a.index),a.verify&&(this.Z=a.verify);c=d[this.c++];b=d[this.c++];switch(c&15){case ub:this.method=ub;break;default:q(Error("unsupported compression method"))}0!==((c<<8)+b)%31&&q(Error("invalid fcheck flag:"+((c<<8)+b)%31));b&32&&q(Error("fdict flag is not supported"));this.K=new W(d,{index:this.c,bufferSize:a.bufferSize,bufferType:a.bufferType,resize:a.resize})}
tb.prototype.h=function(){var d=this.input,a,c;a=this.K.h();this.c=this.K.c;this.Z&&(c=(d[this.c++]<<24|d[this.c++]<<16|d[this.c++]<<8|d[this.c++])>>>0,c!==sb(a)&&q(Error("invalid adler-32 checksum")));return a};var ub=8;function vb(d,a){this.input=d;this.a=new (E?Uint8Array:Array)(32768);this.k=Z.o;var c={},b;if((a||!(a={}))&&"number"===typeof a.compressionType)this.k=a.compressionType;for(b in a)c[b]=a[b];c.outputBuffer=this.a;this.J=new oa(this.input,c)}var Z=sa;
vb.prototype.g=function(){var d,a,c,b,e,f,g,k=0;g=this.a;d=ub;switch(d){case ub:a=Math.LOG2E*Math.log(32768)-8;break;default:q(Error("invalid compression method"))}c=a<<4|d;g[k++]=c;switch(d){case ub:switch(this.k){case Z.NONE:e=0;break;case Z.v:e=1;break;case Z.o:e=2;break;default:q(Error("unsupported compression type"))}break;default:q(Error("invalid compression method"))}b=e<<6|0;g[k++]=b|31-(256*c+b)%31;f=sb(this.input);this.J.b=k;g=this.J.g();k=g.length;E&&(g=new Uint8Array(g.buffer),g.length<=
k+4&&(this.a=new Uint8Array(g.length+4),this.a.set(g),g=this.a),g=g.subarray(0,k+4));g[k++]=f>>24&255;g[k++]=f>>16&255;g[k++]=f>>8&255;g[k++]=f&255;return g};function wb(d,a){var c,b,e,f;if(Object.keys)c=Object.keys(a);else for(b in c=[],e=0,a)c[e++]=b;e=0;for(f=c.length;e<f;++e)b=c[e],B(d+"."+b,a[b])};B("Zlib.Inflate",tb);B("Zlib.Inflate.prototype.decompress",tb.prototype.h);wb("Zlib.Inflate.BufferType",{ADAPTIVE:Ya.M,BLOCK:Ya.N});B("Zlib.Deflate",vb);B("Zlib.Deflate.compress",function(d,a){return(new vb(d,a)).g()});B("Zlib.Deflate.prototype.compress",vb.prototype.g);wb("Zlib.Deflate.CompressionType",{NONE:Z.NONE,FIXED:Z.v,DYNAMIC:Z.o});B("Zlib.Gzip",Ea);B("Zlib.Gzip.prototype.compress",Ea.prototype.g);B("Zlib.Gunzip",rb);B("Zlib.Gunzip.prototype.decompress",rb.prototype.h);B("Zlib.Gunzip.prototype.getMembers",rb.prototype.W);B("Zlib.GunzipMember",T);B("Zlib.GunzipMember.prototype.getName",T.prototype.getName);B("Zlib.GunzipMember.prototype.getData",T.prototype.getData);B("Zlib.GunzipMember.prototype.getMtime",T.prototype.X);}).call(this);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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