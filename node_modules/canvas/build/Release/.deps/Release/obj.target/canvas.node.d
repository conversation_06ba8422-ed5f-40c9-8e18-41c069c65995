cmd_Release/obj.target/canvas.node := g++ -o Release/obj.target/canvas.node -shared -pthread -rdynamic -m64 -Wl,-rpath '-Wl,$$ORIGIN'  -Wl,-soname=canvas.node -Wl,--start-group Release/obj.target/canvas/src/backend/Backend.o Release/obj.target/canvas/src/backend/ImageBackend.o Release/obj.target/canvas/src/backend/PdfBackend.o Release/obj.target/canvas/src/backend/SvgBackend.o Release/obj.target/canvas/src/bmp/BMPParser.o Release/obj.target/canvas/src/Backends.o Release/obj.target/canvas/src/Canvas.o Release/obj.target/canvas/src/CanvasGradient.o Release/obj.target/canvas/src/CanvasPattern.o Release/obj.target/canvas/src/CanvasRenderingContext2d.o Release/obj.target/canvas/src/closure.o Release/obj.target/canvas/src/color.o Release/obj.target/canvas/src/Image.o Release/obj.target/canvas/src/ImageData.o Release/obj.target/canvas/src/init.o Release/obj.target/canvas/src/register_font.o -Wl,--end-group -L/usr/local/lib -lpixman-1 -lcairo -lpng16 -lz -lpangocairo-1.0 -lpango-1.0 -lgobject-2.0 -lglib-2.0 -lharfbuzz -lfreetype -lrsvg-2 -lm -lgio-2.0 -lgdk_pixbuf-2.0 -ljpeg -lgif
