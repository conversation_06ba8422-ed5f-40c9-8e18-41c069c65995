// ========================================
// ⚡ DIRECT POST SENDER - 1 DÒNG LỆNH
// ========================================
// Gửi POST trực tiếp mà không cần form

// 🚀 FUNCTION GỬI POST TRỰC TIẾP
window.sendDirectPost = async function(videoUrl) {
    console.log('🚀 Sending direct POST...');
    
    // Kill countdown trước
    if(window.cdtm) clearInterval(window.cdtm);
    for(let i=0;i<99999;i++) clearInterval(i);
    
    try {
        // Prepare POST data
        const formData = new URLSearchParams();
        formData.append('1ebb17d8fdd93df9', videoUrl); // Form name từ HTML
        
        // Send POST request
        const response = await fetch('https://zefoy.com/c2VuZC9mb2xeb3dlcnNfdGlrdG9V', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': '*/*',
                'Origin': 'https://zefoy.com',
                'Referer': 'https://zefoy.com/',
                'User-Agent': navigator.userAgent
            },
            body: formData,
            credentials: 'same-origin'
        });
        
        const result = await response.text();
        console.log('📥 Response:', result);
        
        // Decode Zefoy response
        try {
            const decoded = atob(decodeURIComponent(result.split('').reverse().join('')));
            console.log('🔓 Decoded:', decoded);
            
            if(decoded.includes('Successfully')) {
                alert('✅ Views sent successfully!');
                return true;
            } else if(decoded.includes('Please wait')) {
                alert('⏳ Need to wait - but countdown is killed!');
                return false;
            }
        } catch(e) {
            console.log('📄 Raw response:', result);
        }
        
        return response.ok;
        
    } catch(error) {
        console.error('❌ Error:', error);
        alert('❌ Error: ' + error.message);
        return false;
    }
};

// 🔄 AUTO LOOP SENDER
window.autoSendLoop = function(videoUrl, intervalSeconds = 10) {
    console.log(`🔄 Starting auto loop every ${intervalSeconds} seconds...`);
    
    const interval = setInterval(async () => {
        console.log('🚀 Auto sending...');
        await sendDirectPost(videoUrl);
    }, intervalSeconds * 1000);
    
    // Stop function
    window.stopAutoSend = () => {
        clearInterval(interval);
        console.log('⏹️ Auto send stopped');
    };
    
    console.log('✅ Auto loop started! Use stopAutoSend() to stop.');
};

// 💥 INSTANT COMMANDS
window.quickSend = (url) => sendDirectPost(url);
window.fastLoop = (url) => autoSendLoop(url, 5); // Every 5 seconds

// ========================================
// 🎯 1-LINER COMMANDS
// ========================================

console.log(`
⚡ DIRECT POST SENDER LOADED!

🚀 QUICK USAGE:

1. Send once:
sendDirectPost('https://tiktok.com/@user/video/123');

2. Auto loop every 10s:
autoSendLoop('https://tiktok.com/@user/video/123', 10);

3. Fast loop every 5s:
fastLoop('https://tiktok.com/@user/video/123');

4. Stop auto:
stopAutoSend();

💡 Replace URL with your TikTok link!
`);

// ========================================
// 🎨 MINI UI
// ========================================
(function createQuickUI() {
    const ui = document.createElement('div');
    ui.innerHTML = `
        <div style="
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.9);
            color: #00ff00;
            padding: 15px;
            border-radius: 10px;
            font-family: monospace;
            font-size: 12px;
            z-index: 999999;
            border: 2px solid #00ff00;
            min-width: 300px;
        ">
            <div style="text-align: center; font-weight: bold; margin-bottom: 10px;">
                ⚡ DIRECT POST SENDER
            </div>
            
            <input type="text" id="quick-url" placeholder="TikTok Video URL" 
                   style="width: 100%; padding: 5px; margin-bottom: 10px; background: #333; color: #00ff00; border: 1px solid #555;">
            
            <div style="text-align: center;">
                <button onclick="sendDirectPost(document.getElementById('quick-url').value)" 
                        style="background: #00ff00; color: #000; border: none; padding: 8px 15px; margin: 2px; border-radius: 5px; cursor: pointer;">
                    📤 SEND NOW
                </button>
                
                <button onclick="autoSendLoop(document.getElementById('quick-url').value, 10)" 
                        style="background: #ffaa00; color: #000; border: none; padding: 8px 15px; margin: 2px; border-radius: 5px; cursor: pointer;">
                    🔄 AUTO LOOP
                </button>
                
                <button onclick="stopAutoSend()" 
                        style="background: #ff0000; color: #fff; border: none; padding: 8px 15px; margin: 2px; border-radius: 5px; cursor: pointer;">
                    ⏹️ STOP
                </button>
            </div>
            
            <div style="font-size: 10px; color: #666; margin-top: 10px; text-align: center;">
                💡 Direct POST to Zefoy endpoint
            </div>
        </div>
    `;
    
    document.body.appendChild(ui);
})();

// ========================================
// 🚀 EXTREME SPEED VERSION
// ========================================

// Gửi POST siêu nhanh không cần UI
window.extremeSpeed = async function(videoUrl) {
    // Kill everything
    for(let i=0;i<99999;i++){clearInterval(i);clearTimeout(i);}
    
    // Send POST immediately
    const data = new URLSearchParams();
    data.append('1ebb17d8fdd93df9', videoUrl);
    
    fetch('https://zefoy.com/c2VuZC9mb2xeb3dlcnNfdGlrdG9V', {
        method: 'POST',
        body: data,
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        credentials: 'same-origin'
    }).then(r => r.text()).then(t => {
        try {
            const d = atob(decodeURIComponent(t.split('').reverse().join('')));
            console.log('⚡ Result:', d.includes('Successfully') ? '✅ SUCCESS' : '❌ FAILED');
        } catch(e) {
            console.log('📄 Raw:', t);
        }
    });
    
    console.log('⚡ Extreme speed POST sent!');
};

console.log(`
💥 EXTREME COMMANDS:

extremeSpeed('https://tiktok.com/@user/video/123');

⚡ Fastest possible execution!
`);
