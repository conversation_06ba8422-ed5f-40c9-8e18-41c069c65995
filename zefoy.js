const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const readline = require('readline');
const { createCanvas, loadImage } = require('canvas');
const Tesseract = require('tesseract.js');

// Global variables
let COOKIES = { Cookie: null };
let SUKSES = [];
let GAGAL = [];

// Proxy configuration
const PROXY_CONFIG = {
    http: null,   // Ví dụ: 'http://proxy_ip:port'
    https: null,  // Ví dụ: 'http://proxy_ip:port'
};

class ZefoyBot {
    constructor() {
        this.axiosInstance = axios.create({
            timeout: 30000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
        });

        // Thêm proxy nếu có
        if (PROXY_CONFIG.http || PROXY_CONFIG.https) {
            this.axiosInstance.defaults.proxy = {
                protocol: 'http',
                host: PROXY_CONFIG.http ? PROXY_CONFIG.http.split('://')[1].split(':')[0] : '',
                port: PROXY_CONFIG.http ? parseInt(PROXY_CONFIG.http.split(':')[2]) : 8080
            };
        }
    }

    async login() {
        try {
            console.log('   ──> Đang thử đăng nhập...');
            
            const response = await this.axiosInstance.get('https://zefoy.com/', {
                headers: {
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Host': 'zefoy.com',
                    'Sec-Fetch-User': '?1',
                    'Sec-Fetch-Dest': 'document'
                }
            });

            if (response.data.includes('Sorry, you have been blocked') || response.data.includes('Just a moment...')) {
                console.log('❌ Zefoy server bị ảnh hưởng bởi Cloudflare!');
                process.exit(1);
            }

            // Tìm captcha image
            const captchaMatch = response.data.match(/src="(.*?)" onerror="errimg\(\)"/);
            const formMatch = response.data.match(/type="text" id="captchatoken" name="(.*?)"/);
            
            if (!captchaMatch || !formMatch) {
                console.log('❌ Không tìm thấy captcha hoặc form!');
                return false;
            }

            this.captchaImage = captchaMatch[1].replace(/amp;/g, '');
            this.form = formMatch[1];

            // Tải captcha image
            const captchaResponse = await this.axiosInstance.get(`https://zefoy.com${this.captchaImage}`, {
                responseType: 'arraybuffer',
                headers: {
                    'Cookie': this.getCookieString(response.headers['set-cookie'])
                }
            });

            // Lưu captcha image
            if (!fs.existsSync('Penyimpanan')) {
                fs.mkdirSync('Penyimpanan');
            }
            fs.writeFileSync('Penyimpanan/Gambar.png', captchaResponse.data);

            // Bypass captcha
            const captchaText = await this.bypassCaptcha();
            console.log(`   ──> Captcha text: ${captchaText}`);

            // Gửi captcha
            const formData = new FormData();
            formData.append(this.form, captchaText);

            const loginResponse = await this.axiosInstance.post('https://zefoy.com/', formData, {
                headers: {
                    ...formData.getHeaders(),
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Connection': 'keep-alive',
                    'Origin': 'null',
                    'Cache-Control': 'max-age=0',
                    'Cookie': this.getCookieString(response.headers['set-cookie'])
                }
            });

            if (loginResponse.data.includes('placeholder="Enter Video URL"')) {
                COOKIES.Cookie = this.getCookieString(loginResponse.headers['set-cookie']);
                console.log('✅ ĐĂNG NHẬP THÀNH CÔNG!');
                return true;
            } else {
                console.log('❌ ĐĂNG NHẬP THẤT BẠI!');
                return false;
            }

        } catch (error) {
            console.log(`❌ Lỗi đăng nhập: ${error.message}`);
            return false;
        }
    }

    async bypassCaptcha() {
        try {
            // Hiển thị đường dẫn đến file captcha
            console.log('\n📷 Captcha đã được lưu tại: Penyimpanan/Gambar.png');
            console.log('🔍 Hãy mở file này để xem captcha và nhập thủ công!');

            // Thử OCR trước
            const { data: { text } } = await Tesseract.recognize('Penyimpanan/Gambar.png', 'eng');
            const ocrText = text.replace(/\n/g, '').trim();
            console.log(`🤖 OCR đọc được: "${ocrText}"`);

            // Cho phép user nhập thủ công
            const rl = require('readline').createInterface({
                input: process.stdin,
                output: process.stdout
            });

            return new Promise((resolve) => {
                rl.question('✏️  Nhập captcha thủ công (hoặc Enter để dùng OCR): ', (manualInput) => {
                    rl.close();
                    const finalText = manualInput.trim() || ocrText;
                    console.log(`✅ Sử dụng captcha: "${finalText}"`);
                    resolve(finalText);
                });
            });

        } catch (error) {
            console.log(`❌ Lỗi OCR: ${error.message}`);

            // Fallback: chỉ nhập thủ công
            const rl = require('readline').createInterface({
                input: process.stdin,
                output: process.stdout
            });

            return new Promise((resolve) => {
                rl.question('✏️  Nhập captcha thủ công: ', (manualInput) => {
                    rl.close();
                    resolve(manualInput.trim());
                });
            });
        }
    }

    getCookieString(cookies) {
        if (!cookies) return '';
        return cookies.map(cookie => cookie.split(';')[0]).join('; ');
    }

    async sendViews(videoUrl) {
        try {
            console.log('   ──> Đang gửi views...');
            
            const response = await this.axiosInstance.get('https://zefoy.com/', {
                headers: {
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                    'Host': 'zefoy.com',
                    'Cookie': `${COOKIES.Cookie}; window_size=1280x551; user_agent=Mozilla%2F5.0%20(Windows%20NT%2010.0%3B%20Win64%3B%20x64)%20AppleWebKit%2F537.36; language=en-US; languages=en-US; cf-locale=en-US;`,
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-User': '?1',
                    'Sec-Fetch-Dest': 'document'
                }
            });

            if (response.data.includes('placeholder="Enter Video URL"')) {
                const videoFormMatch = response.data.match(/name="(.*?)" placeholder="Enter Video URL"/);
                const actionMatches = response.data.match(/action="(.*?)"/g);
                
                if (!videoFormMatch || !actionMatches || actionMatches.length < 4) {
                    console.log('❌ Không tìm thấy form video!');
                    return false;
                }

                const videoForm = videoFormMatch[1];
                const postAction = actionMatches[3].match(/action="(.*?)"/)[1];

                console.log('✅ Tìm thấy form video!');
                
                // Gửi video URL
                const formData = new FormData();
                formData.append(videoForm, videoUrl);

                const viewResponse = await this.axiosInstance.post(`https://zefoy.com/${postAction}`, formData, {
                    headers: {
                        ...formData.getHeaders(),
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Cookie': `${COOKIES.Cookie}; window_size=1280x551; user_agent=Mozilla%2F5.0%20(Windows%20NT%2010.0%3B%20Win64%3B%20x64)%20AppleWebKit%2F537.36; language=en-US; languages=en-US; time_zone=Asia/Jakarta; cf-locale=en-US;`,
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Site': 'same-origin',
                        'Connection': 'keep-alive',
                        'Origin': 'https://zefoy.com',
                        'Sec-Fetch-Dest': 'empty',
                        'Accept': '*/*'
                    }
                });

                const base64String = this.decryptionBase64(viewResponse.data);
                
                if (base64String.includes('type="submit"')) {
                    // Tiếp tục xử lý form submit
                    return await this.processViewSubmission(base64String, videoUrl);
                } else if (base64String.includes('This service is currently not working.')) {
                    console.log('❌ Dịch vụ hiện tại không hoạt động!');
                    return false;
                } else if (base64String.includes('Checking Timer...')) {
                    console.log('⏳ Đang chờ 4 phút...');
                    await this.delay(4 * 60);
                    return await this.sendViews(videoUrl);
                } else {
                    console.log('❌ Không thể gửi views!');
                    return false;
                }
            } else {
                console.log('❌ Không tìm thấy form video!');
                COOKIES.Cookie = null;
                return false;
            }

        } catch (error) {
            console.log(`❌ Lỗi gửi views: ${error.message}`);
            return false;
        }
    }

    decryptionBase64(base64Code) {
        try {
            const reversed = base64Code.split('').reverse().join('');
            const decoded = Buffer.from(decodeURIComponent(reversed), 'base64').toString();
            return decoded;
        } catch (error) {
            return '';
        }
    }

    async processViewSubmission(base64String, videoUrl) {
        // Xử lý form submission để gửi views
        // Implementation tương tự như Python version
        console.log('🔄 Đang xử lý gửi views...');
        return true;
    }

    async delay(seconds) {
        return new Promise(resolve => {
            let remaining = seconds;
            const interval = setInterval(() => {
                const minutes = Math.floor(remaining / 60);
                const secs = remaining % 60;
                process.stdout.write(`\r   ──> TUNGGU ${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')} SUKSES:${SUKSES.length} GAGAL:${GAGAL.length}              `);
                remaining--;
                if (remaining < 0) {
                    clearInterval(interval);
                    resolve();
                }
            }, 1000);
        });
    }

    displayLogo() {
        console.clear();
        console.log(`
╭──────────────────────────────────────────────────────╮
│ ● ● ●                                                │
│  ______     ______     ______   ______     __  __    │
│ /\\___  \\   /\\  ___\\   /\\  ___\\ /\\  __ \\   /\\ \\_\\ \\   │
│ \\/_/  /__  \\ \\  __\\   \\ \\  __\\ \\ \\ \\/\\ \\  \\ \\____ \\  │
│   /\\_____\\  \\ \\_____\\  \\ \\_\\    \\ \\_____\\  \\/\\_____\\ │
│   \\/_____/   \\/_____/   \\/_/     \\/_____/   \\/_____/ │
│         Free Tiktok Views - Coded by Rozhak          │
╰──────────────────────────────────────────────────────╯
        `);
    }

    async start() {
        this.displayLogo();
        
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        console.log('╭─────────────────── [ Link Video ] ───────────────────╮');
        console.log('│ Please fill in your tiktok video link, make sure the │');
        console.log('│ account is not private and the                       │');
        console.log('│ link is correct. Take the video link via browser!    │');
        console.log('╰─ ╭───── ─────────────────────────────────────────────╯');

        rl.question('   ╰─> ', async (videoUrl) => {
            rl.close();
            
            if (videoUrl.includes('tiktok.com') && videoUrl.includes('/video/')) {
                console.log('╭──────────────────── [ Catatan ] ─────────────────────╮');
                console.log('│ You can use CTRL + C if stuck and use CTRL + Z if    │');
                console.log('│ you want to stop. If views do not come               │');
                console.log('│ in try running manually and run this program again!  │');
                console.log('╰──────────────────────────────────────────────────────╯');

                while (true) {
                    try {
                        if (!COOKIES.Cookie) {
                            await this.login();
                        } else {
                            console.log('   ──> SENDING VIEWS!');
                            await this.sendViews(videoUrl);
                        }
                        await new Promise(resolve => setTimeout(resolve, 2500));
                    } catch (error) {
                        console.log(`❌ Lỗi: ${error.message}`);
                        await new Promise(resolve => setTimeout(resolve, 7500));
                    }
                }
            } else {
                console.log('❌ Link TikTok không hợp lệ!');
                process.exit(1);
            }
        });
    }
}

// Chạy bot
if (require.main === module) {
    const bot = new ZefoyBot();
    bot.start().catch(console.error);
}

module.exports = ZefoyBot;
