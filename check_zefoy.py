#!/usr/bin/env python3
"""
Script kiểm tra trạng thái Zefoy và Cloudflare
"""

import requests
import time
from rich import print as printf
from rich.panel import Panel

def check_zefoy_status():
    """Kiểm tra trạng thái <PERSON>efoy"""
    
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Host': 'zefoy.com',
        'Sec-Ch-Ua': '"Google Chrome";v="134", "Chromium";v="134", "Not-A.Brand";v="99"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    
    try:
        printf("[bold yellow]🔍 Đang kiểm tra trạng thái Zefoy...")
        
        response = requests.get('https://zefoy.com/', headers=headers, timeout=10)
        
        printf(f"[bold cyan]📊 Status Code: {response.status_code}")
        printf(f"[bold cyan]📡 Server: {response.headers.get('server', 'Unknown')}")
        
        # Kiểm tra Cloudflare
        if 'cloudflare' in response.headers.get('server', '').lower():
            printf("[bold red]☁️  Cloudflare detected!")
            
        if 'cf-ray' in response.headers:
            printf(f"[bold red]🌩️  CF-Ray: {response.headers.get('cf-ray')}")
            
        if 'cf-mitigated' in response.headers:
            printf(f"[bold red]🚫 CF-Mitigated: {response.headers.get('cf-mitigated')}")
            
        # Kiểm tra nội dung response
        content = response.text.lower()
        
        if response.status_code == 403:
            printf(Panel("[bold red]❌ BỊCHẶN BỞI CLOUDFLARE!\n\nZefoy đang được bảo vệ bởi Cloudflare và chặn các request tự động.\n\nGiải pháp:\n1. Sử dụng VPN/Proxy\n2. Đợi một thời gian rồi thử lại\n3. Sử dụng browser thật để bypass", 
                         width=60, style="bold red", title="[bold red][ CLOUDFLARE BLOCK ]"))
            return False
            
        elif 'just a moment' in content or 'checking your browser' in content:
            printf(Panel("[bold yellow]⏳ CLOUDFLARE CHALLENGE!\n\nZefoy đang yêu cầu challenge từ Cloudflare.\n\nCần bypass challenge này trước khi sử dụng bot.", 
                         width=60, style="bold yellow", title="[bold yellow][ CLOUDFLARE CHALLENGE ]"))
            return False
            
        elif 'enter video url' in content or 'placeholder="enter video url"' in content:
            printf(Panel("[bold green]✅ ZEFOY HOẠT ĐỘNG BÌNH THƯỜNG!\n\nWebsite có thể truy cập được và form video đã xuất hiện.\n\nCó thể sử dụng bot.", 
                         width=60, style="bold green", title="[bold green][ SUCCESS ]"))
            return True
            
        else:
            printf(Panel(f"[bold orange]⚠️  TRẠNG THÁI KHÔNG XÁC ĐỊNH!\n\nStatus: {response.status_code}\nContent length: {len(content)} chars\n\nCần kiểm tra thủ công.", 
                         width=60, style="bold orange", title="[bold orange][ UNKNOWN ]"))
            return False
            
    except requests.exceptions.Timeout:
        printf(Panel("[bold red]⏰ TIMEOUT!\n\nKhông thể kết nối đến Zefoy trong thời gian cho phép.\n\nKiểm tra kết nối internet.", 
                     width=60, style="bold red", title="[bold red][ TIMEOUT ]"))
        return False
        
    except requests.exceptions.ConnectionError:
        printf(Panel("[bold red]🔌 CONNECTION ERROR!\n\nKhông thể kết nối đến Zefoy.\n\nKiểm tra:\n1. Kết nối internet\n2. DNS settings\n3. Firewall", 
                     width=60, style="bold red", title="[bold red][ CONNECTION ERROR ]"))
        return False
        
    except Exception as e:
        printf(Panel(f"[bold red]❌ LỖI KHÔNG XÁC ĐỊNH!\n\n{str(e)}", 
                     width=60, style="bold red", title="[bold red][ ERROR ]"))
        return False

def suggest_solutions():
    """Đưa ra các giải pháp"""
    printf("\n[bold cyan]💡 CÁC GIẢI PHÁP KHUYẾN NGHỊ:")
    printf("[bold white]1. 🌐 Sử dụng VPN:")
    printf("   - ProtonVPN (miễn phí)")
    printf("   - NordVPN, ExpressVPN (trả phí)")
    printf("   - Tor Browser")
    
    printf("\n[bold white]2. 🔄 Thay đổi IP:")
    printf("   - Restart router/modem")
    printf("   - Sử dụng mobile hotspot")
    printf("   - Proxy servers")
    
    printf("\n[bold white]3. ⏰ Đợi và thử lại:")
    printf("   - Cloudflare block thường tạm thời")
    printf("   - Thử lại sau 1-2 giờ")
    printf("   - Thử vào giờ khác trong ngày")
    
    printf("\n[bold white]4. 🌍 Thử từ location khác:")
    printf("   - VPS từ country khác")
    printf("   - Cloud shell (Google, AWS)")

if __name__ == "__main__":
    printf(Panel("[bold cyan]ZEFOY STATUS CHECKER\n\nKiểm tra trạng thái Zefoy và Cloudflare protection", 
                 width=60, style="bold cyan", title="[bold cyan][ ZEFOY CHECKER ]"))
    
    success = check_zefoy_status()
    
    if not success:
        suggest_solutions()
    
    printf(f"\n[bold white]⏰ Thời gian kiểm tra: {time.strftime('%Y-%m-%d %H:%M:%S')}")
