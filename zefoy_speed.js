// ========================================
// ⚡ ZEFOY SPEED BOOSTER - CONSOLE SCRIPT
// ========================================
// Tăng tốc độ và tự động hóa các thao tác

(function() {
    'use strict';
    
    // ========================================
    // ⚡ SIÊU NHANH CONFIGURATION
    // ========================================
    const SPEED_CONFIG = {
        AUTO_FILL_DELAY: 100,      // 0.1s - SIÊU NHANH!
        SUBMIT_DELAY: 200,         // 0.2s - SIÊU NHANH!
        CHECK_INTERVAL: 500,       // Check mỗi 0.5s - NHANH GẤP 4 LẦN!
        CAPTCHA_CHECK_INTERVAL: 200, // Check captcha mỗi 0.2s - SIÊU NHANH!
        MAX_WAIT_TIME: 300000,     // 5 phút timeout
        RETRY_DELAY: 1000,         // 1s retry - NHANH GẤP 5 LẦN!
        TURBO_MODE: true           // BẬT TURBO MODE!
    };
    
    // ========================================
    // 🎨 MINI UI
    // ========================================
    function createSpeedPanel() {
        const panel = document.createElement('div');
        panel.id = 'zefoy-speed-panel';
        panel.innerHTML = `
            <div style="
                position: fixed;
                top: 10px;
                left: 10px;
                background: rgba(0,0,0,0.9);
                color: #00ff00;
                padding: 15px;
                border-radius: 10px;
                font-family: monospace;
                font-size: 12px;
                z-index: 999999;
                border: 2px solid #00ff00;
                min-width: 250px;
            ">
                <div style="text-align: center; font-weight: bold; margin-bottom: 10px;">
                    ⚡ ZEFOY SPEED BOOSTER
                </div>
                
                <div style="margin-bottom: 10px;">
                    <input type="text" id="speed-video-url" placeholder="TikTok Video URL" 
                           style="width: 100%; padding: 5px; background: #333; color: #00ff00; border: 1px solid #555;">
                </div>
                
                <div style="text-align: center; margin-bottom: 10px;">
                    <button id="speed-start" style="background: #00ff00; color: #000; border: none; padding: 8px 15px; margin: 2px; border-radius: 5px; cursor: pointer;">START</button>
                    <button id="speed-stop" style="background: #ff0000; color: #fff; border: none; padding: 8px 15px; margin: 2px; border-radius: 5px; cursor: pointer;">STOP</button>
                </div>
                
                <div id="speed-status" style="text-align: center; padding: 5px; background: #333; border-radius: 5px; margin-bottom: 10px;">
                    Status: Ready
                </div>
                
                <div id="speed-stats" style="font-size: 10px;">
                    <div>Success: <span id="speed-success">0</span></div>
                    <div>Failed: <span id="speed-failed">0</span></div>
                    <div>Speed: <span id="speed-rate">0</span>/min</div>
                </div>
                
                <div style="font-size: 10px; color: #666; margin-top: 10px;">
                    💡 Auto-fill, auto-submit, speed optimized
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
        return panel;
    }
    
    // ========================================
    // ⚡ SPEED BOOSTER CLASS
    // ========================================
    class ZefoySpeedBooster {
        constructor() {
            this.isRunning = false;
            this.stats = { success: 0, failed: 0, startTime: null };
            this.videoUrl = '';
            this.panel = null;
            
            this.init();
        }
        
        init() {
            console.log('⚡ Initializing Speed Booster...');
            this.panel = createSpeedPanel();
            this.setupEvents();
            this.optimizePage();
        }
        
        setupEvents() {
            document.getElementById('speed-start').onclick = () => this.start();
            document.getElementById('speed-stop').onclick = () => this.stop();
        }
        
        optimizePage() {
            // Disable animations
            const style = document.createElement('style');
            style.textContent = `
                *, *::before, *::after {
                    animation-duration: 0.01ms !important;
                    animation-delay: -1ms !important;
                    transition-duration: 0.01ms !important;
                    transition-delay: -1ms !important;
                }
            `;
            document.head.appendChild(style);
            
            // Remove unnecessary elements
            const unnecessarySelectors = [
                'script[src*="google"]',
                'script[src*="facebook"]',
                'script[src*="analytics"]',
                '.advertisement',
                '.ads'
            ];
            
            unnecessarySelectors.forEach(selector => {
                document.querySelectorAll(selector).forEach(el => el.remove());
            });
            
            console.log('⚡ Page optimized for speed');
        }
        
        updateStatus(message) {
            document.getElementById('speed-status').textContent = `Status: ${message}`;
        }
        
        updateStats() {
            document.getElementById('speed-success').textContent = this.stats.success;
            document.getElementById('speed-failed').textContent = this.stats.failed;
            
            if (this.stats.startTime) {
                const elapsed = (Date.now() - this.stats.startTime) / 60000; // minutes
                const rate = Math.round((this.stats.success + this.stats.failed) / elapsed);
                document.getElementById('speed-rate').textContent = rate || 0;
            }
        }
        
        async start() {
            this.videoUrl = document.getElementById('speed-video-url').value.trim();
            
            if (!this.videoUrl || !this.videoUrl.includes('tiktok.com')) {
                alert('❌ Enter valid TikTok URL!');
                return;
            }
            
            this.isRunning = true;
            this.stats.startTime = Date.now();
            this.updateStatus('Starting...');
            
            console.log('⚡ Speed Booster started!');
            await this.speedLoop();
        }
        
        stop() {
            this.isRunning = false;
            this.updateStatus('Stopped');
            console.log('⚡ Speed Booster stopped!');
        }
        
        async speedLoop() {
            while (this.isRunning) {
                try {
                    if (this.isLoggedIn()) {
                        await this.fastSendViews();
                    } else if (this.hasCaptcha()) {
                        await this.fastCaptchaHandle();
                    } else {
                        this.updateStatus('Waiting for page...');
                        await this.delay(SPEED_CONFIG.CHECK_INTERVAL);
                    }
                } catch (error) {
                    console.error('⚡ Speed loop error:', error);
                    this.stats.failed++;
                    this.updateStats();
                    await this.delay(SPEED_CONFIG.RETRY_DELAY);
                }
            }
        }
        
        isLoggedIn() {
            return !!document.querySelector('input[placeholder*="Enter Video URL"]');
        }
        
        hasCaptcha() {
            return !!document.querySelector('#captchatoken');
        }
        
        async fastCaptchaHandle() {
            this.updateStatus('Captcha detected - solve manually');
            
            // Highlight captcha for quick solving
            const captchaImg = document.querySelector('img[src*="captcha"]');
            const captchaInput = document.querySelector('#captchatoken');
            
            if (captchaImg) {
                captchaImg.style.border = '3px solid #ff0000';
                captchaImg.style.animation = 'blink 1s infinite';
            }
            
            if (captchaInput) {
                captchaInput.style.border = '3px solid #ff0000';
                captchaInput.focus();
                
                // Auto-submit when user types
                captchaInput.oninput = () => {
                    if (captchaInput.value.length >= 4) {
                        setTimeout(() => {
                            const submitBtn = document.querySelector('input[type="submit"]');
                            if (submitBtn) submitBtn.click();
                        }, 500);
                    }
                };
            }
            
            // Wait for captcha solve
            let attempts = 0;
            while (this.isRunning && this.hasCaptcha() && attempts < 60) {
                await this.delay(SPEED_CONFIG.CAPTCHA_CHECK_INTERVAL);
                attempts++;
            }
        }
        
        async fastSendViews() {
            this.updateStatus('Sending views...');
            
            try {
                // Super fast fill
                const videoInput = document.querySelector('input[placeholder*="Enter Video URL"]');
                if (videoInput) {
                    videoInput.value = this.videoUrl;
                    videoInput.dispatchEvent(new Event('input', { bubbles: true }));
                    videoInput.dispatchEvent(new Event('change', { bubbles: true }));
                }
                
                await this.delay(SPEED_CONFIG.AUTO_FILL_DELAY);
                
                // Super fast submit
                const submitBtn = this.findSubmitButton();
                if (submitBtn) {
                    submitBtn.click();
                    
                    await this.delay(SPEED_CONFIG.SUBMIT_DELAY);
                    
                    // Quick result check
                    const result = this.checkResult();
                    
                    if (result.success) {
                        this.stats.success++;
                        this.updateStatus('✅ Success!');
                        console.log('⚡ Views sent successfully!');
                    } else {
                        this.stats.failed++;
                        this.updateStatus('❌ Failed');
                        console.log('⚡ Failed to send views');
                    }
                    
                    this.updateStats();
                    
                    // Minimal delay before next attempt
                    await this.delay(result.waitTime || 10000);
                    
                } else {
                    this.updateStatus('No submit button');
                    await this.delay(SPEED_CONFIG.CHECK_INTERVAL);
                }
                
            } catch (error) {
                console.error('⚡ Fast send error:', error);
                this.stats.failed++;
                this.updateStats();
            }
        }
        
        findSubmitButton() {
            const selectors = [
                'input[type="submit"][value*="Send"]',
                'input[value*="Send"]',
                'button[type="submit"]',
                'input[type="submit"]'
            ];
            
            for (const selector of selectors) {
                const btn = document.querySelector(selector);
                if (btn && btn.offsetParent !== null) { // visible
                    return btn;
                }
            }
            
            return null;
        }
        
        checkResult() {
            const pageText = document.body.innerText.toLowerCase();
            
            if (pageText.includes('successfully') && pageText.includes('views sent')) {
                return { success: true, waitTime: 15000 };
            } else if (pageText.includes('please wait')) {
                const waitMatch = pageText.match(/wait (\d+) seconds/);
                const waitTime = waitMatch ? parseInt(waitMatch[1]) * 1000 : 60000;
                return { success: false, waitTime };
            } else if (pageText.includes('too many requests')) {
                return { success: false, waitTime: 120000 };
            } else {
                return { success: false, waitTime: 30000 };
            }
        }
        
        async delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        destroy() {
            this.stop();
            if (this.panel) this.panel.remove();
        }
    }
    
    // ========================================
    // 🚀 ADDITIONAL SPEED HACKS
    // ========================================
    
    // Override setTimeout for faster execution
    const originalSetTimeout = window.setTimeout;
    window.setTimeout = function(callback, delay, ...args) {
        const speedMultiplier = 0.1; // 10x faster
        return originalSetTimeout(callback, Math.max(1, delay * speedMultiplier), ...args);
    };
    
    // Speed up intervals
    const originalSetInterval = window.setInterval;
    window.setInterval = function(callback, delay, ...args) {
        const speedMultiplier = 0.5; // 2x faster
        return originalSetInterval(callback, Math.max(100, delay * speedMultiplier), ...args);
    };
    
    // Disable unnecessary network requests
    const originalFetch = window.fetch;
    window.fetch = function(url, options) {
        if (typeof url === 'string') {
            if (url.includes('google') || url.includes('facebook') || 
                url.includes('analytics') || url.includes('ads')) {
                return Promise.resolve(new Response('{}'));
            }
        }
        return originalFetch(url, options);
    };
    
    // ========================================
    // 🚀 AUTO START
    // ========================================
    
    if (window.zefoySpeedBooster) {
        window.zefoySpeedBooster.destroy();
    }
    
    window.zefoySpeedBooster = new ZefoySpeedBooster();
    
    // Global commands
    window.speedStart = () => window.zefoySpeedBooster.start();
    window.speedStop = () => window.zefoySpeedBooster.stop();
    
    console.log(`
    ⚡ ZEFOY SPEED BOOSTER LOADED!
    
    🚀 Features:
    - 10x faster timeouts
    - Auto-fill & auto-submit
    - Optimized page performance
    - Real-time statistics
    
    📋 Commands:
    - speedStart() : Start speed bot
    - speedStop()  : Stop speed bot
    
    💡 Speed panel appeared on top-left!
    `);
    
})();
