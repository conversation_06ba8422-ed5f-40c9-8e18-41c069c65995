#!/usr/bin/env python3
"""
Zefoy <PERSON> vớ<PERSON> Playwright - Stealth mode cao cấp
"""

import asyncio
import time
from playwright.async_api import async_playwright
from rich import print as printf
from rich.panel import Panel
from rich.console import Console

class Z<PERSON><PERSON><PERSON><PERSON>wright:
    def __init__(self):
        self.page = None
        self.browser = None
        self.context = None
    
    async def setup_browser(self):
        """Thiết lập browser với stealth mode"""
        try:
            printf("[bold yellow]🚀 Đang khởi động Playwright...")
            
            playwright = await async_playwright().start()
            
            # Khởi động browser với stealth options
            self.browser = await playwright.chromium.launch(
                headless=False,  # Hiển thị browser để user thấy
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-features=VizDisplayCompositor',
                    '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                ]
            )
            
            # Tạo context với stealth settings
            self.context = await self.browser.new_context(
                viewport={'width': 1280, 'height': 720},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                locale='en-US',
                timezone_id='America/New_York'
            )
            
            # Tạo page
            self.page = await self.context.new_page()
            
            # Inject stealth scripts
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
                
                window.chrome = {
                    runtime: {},
                };
            """)
            
            printf("[bold green]✅ Playwright đã sẵn sàng!")
            return True
            
        except Exception as e:
            printf(f"[bold red]❌ Lỗi khởi động Playwright: {e}")
            return False
    
    async def bypass_cloudflare(self):
        """Bypass Cloudflare với Playwright"""
        try:
            printf("[bold yellow]☁️  Đang truy cập Zefoy...")
            
            await self.page.goto("https://zefoy.com", wait_until="networkidle")
            
            # Đợi page load hoàn toàn
            await asyncio.sleep(5)
            
            # Kiểm tra Cloudflare
            page_content = await self.page.content()
            
            if "Just a moment" in page_content or "Checking your browser" in page_content:
                printf("[bold yellow]⏳ Đang chờ Cloudflare...")
                
                # Đợi Cloudflare tự động bypass
                for i in range(60):  # Đợi tối đa 60 giây
                    await asyncio.sleep(1)
                    content = await self.page.content()
                    
                    if "Enter Video URL" in content:
                        printf("[bold green]✅ Đã bypass Cloudflare!")
                        return True
                    
                    if i % 5 == 0:
                        printf(f"[bold yellow]⏳ Đợi Cloudflare... {i}/60s")
                
                printf("[bold red]❌ Timeout bypass Cloudflare!")
                return False
            
            elif "Enter Video URL" in page_content:
                printf("[bold green]✅ Truy cập thành công!")
                return True
            
            else:
                printf("[bold red]❌ Trang không load đúng!")
                return False
                
        except Exception as e:
            printf(f"[bold red]❌ Lỗi truy cập: {e}")
            return False
    
    async def solve_captcha(self):
        """Giải captcha với Playwright"""
        try:
            printf("[bold yellow]🔍 Đang tìm captcha...")
            
            # Đợi captcha xuất hiện
            await self.page.wait_for_selector("img[src*='captcha']", timeout=10000)
            
            printf("[bold green]📷 Đã tìm thấy captcha!")
            printf("[bold cyan]👀 Hãy nhìn vào browser để xem captcha!")
            
            # Cho user nhập captcha
            console = Console()
            captcha_text = console.input("[bold green]✏️  Nhập captcha từ browser: ")
            
            # Tìm input captcha và nhập
            captcha_input = await self.page.query_selector("#captchatoken")
            if captcha_input:
                await captcha_input.clear()
                await captcha_input.type(captcha_text)
                
                # Submit form
                submit_btn = await self.page.query_selector("input[type='submit']")
                if submit_btn:
                    await submit_btn.click()
                    
                    # Đợi kết quả
                    await asyncio.sleep(3)
                    
                    content = await self.page.content()
                    if "Enter Video URL" in content:
                        printf("[bold green]✅ Đăng nhập thành công!")
                        return True
                    else:
                        printf("[bold red]❌ Captcha sai!")
                        return False
            
            printf("[bold red]❌ Không tìm thấy form captcha!")
            return False
            
        except Exception as e:
            printf(f"[bold red]❌ Lỗi giải captcha: {e}")
            return False
    
    async def send_views(self, video_url):
        """Gửi views với Playwright"""
        try:
            printf(f"[bold yellow]📹 Đang gửi views...")
            
            # Tìm input video URL
            video_input = await self.page.query_selector("input[placeholder*='Enter Video URL']")
            if not video_input:
                printf("[bold red]❌ Không tìm thấy input video!")
                return False
            
            # Nhập URL
            await video_input.clear()
            await video_input.type(video_url)
            
            # Tìm và click submit
            submit_btn = await self.page.query_selector("input[type='submit'][value*='Send']")
            if submit_btn:
                await submit_btn.click()
                
                # Đợi kết quả
                await asyncio.sleep(5)
                
                content = await self.page.content()
                
                if "Successfully" in content and "views sent" in content:
                    printf("[bold green]✅ Gửi views thành công!")
                    return True
                elif "Please wait" in content:
                    printf("[bold yellow]⏳ Cần đợi...")
                    return False
                elif "This service is currently not working" in content:
                    printf("[bold red]❌ Dịch vụ không hoạt động!")
                    return False
                else:
                    printf("[bold red]❌ Không thể gửi views!")
                    return False
            
            printf("[bold red]❌ Không tìm thấy nút submit!")
            return False
            
        except Exception as e:
            printf(f"[bold red]❌ Lỗi gửi views: {e}")
            return False
    
    async def run(self):
        """Chạy bot chính"""
        try:
            printf(Panel("[bold cyan]ZEFOY PLAYWRIGHT BOT\n\nSử dụng Playwright stealth mode", 
                         width=60, style="bold cyan", title="[bold cyan][ PLAYWRIGHT BOT ]"))
            
            # Khởi động browser
            if not await self.setup_browser():
                return
            
            # Bypass Cloudflare
            if not await self.bypass_cloudflare():
                return
            
            # Đăng nhập
            login_success = False
            for attempt in range(5):
                printf(f"[bold yellow]🔑 Thử đăng nhập lần {attempt + 1}/5...")
                
                if await self.solve_captcha():
                    login_success = True
                    break
                
                if attempt < 4:
                    await self.page.reload()
                    await asyncio.sleep(3)
            
            if not login_success:
                printf("[bold red]❌ Không thể đăng nhập!")
                return
            
            # Nhập video URL
            console = Console()
            video_url = console.input("[bold green]📹 Nhập link video TikTok: ")
            
            if not ('tiktok.com' in video_url and '/video/' in video_url):
                printf("[bold red]❌ Link TikTok không hợp lệ!")
                return
            
            # Gửi views liên tục
            success_count = 0
            fail_count = 0
            
            printf("[bold green]🚀 Bắt đầu gửi views...")
            
            while True:
                try:
                    if await self.send_views(video_url):
                        success_count += 1
                        printf(f"[bold green]📊 Thành công: {success_count} | Thất bại: {fail_count}")
                        await asyncio.sleep(30)
                    else:
                        fail_count += 1
                        printf(f"[bold red]📊 Thành công: {success_count} | Thất bại: {fail_count}")
                        await asyncio.sleep(60)
                        
                        # Refresh nếu fail nhiều
                        if fail_count % 3 == 0:
                            printf("[bold yellow]🔄 Refresh trang...")
                            await self.page.reload()
                            await asyncio.sleep(5)
                
                except KeyboardInterrupt:
                    printf("\n[bold yellow]⏹️  Dừng bot!")
                    break
                except Exception as e:
                    printf(f"[bold red]❌ Lỗi: {e}")
                    await asyncio.sleep(10)
        
        finally:
            if self.browser:
                await self.browser.close()
                printf("[bold green]✅ Đã đóng browser!")

async def main():
    bot = ZefoyPlaywright()
    await bot.run()

if __name__ == "__main__":
    asyncio.run(main())
