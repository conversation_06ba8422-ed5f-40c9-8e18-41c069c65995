// ========================================
// 🚀 ZEFOY AUTO POST SENDER - SIÊU NHANH
// ========================================
// Tự động gửi POST request và bypass countdown

(function() {
    'use strict';
    
    console.log('🚀 ZEFOY AUTO POST SENDER LOADING...');
    
    // ========================================
    // 🎯 CONFIGURATION
    // ========================================
    const CONFIG = {
        POST_URL: 'https://zefoy.com/c2VuZC9mb2xeb3dlcnNfdGlrdG9V',
        FORM_NAME: '1ebb17d8fdd93df9', // Từ HTML bạn cung cấp
        VIDEO_URL: '', // Sẽ được set
        AUTO_RETRY: true,
        RETRY_DELAY: 5000, // 5 giây
        MAX_RETRIES: 999
    };
    
    let isRunning = false;
    let retryCount = 0;
    let stats = { success: 0, failed: 0, total: 0 };
    
    // ========================================
    // 🎨 UI PANEL
    // ========================================
    function createAutoPostUI() {
        const panel = document.createElement('div');
        panel.id = 'auto-post-panel';
        panel.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                width: 320px;
                background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
                color: #00ff00;
                border: 2px solid #00ff00;
                border-radius: 15px;
                padding: 20px;
                z-index: 999999;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                box-shadow: 0 0 30px rgba(0, 255, 0, 0.5);
            ">
                <div style="text-align: center; font-size: 16px; font-weight: bold; margin-bottom: 15px; text-shadow: 0 0 10px #00ff00;">
                    🚀 AUTO POST SENDER
                </div>
                
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px;">📹 TikTok URL:</label>
                    <input type="text" id="auto-video-url" placeholder="https://tiktok.com/@user/video/123" 
                           style="width: 100%; padding: 8px; background: #333; color: #00ff00; border: 1px solid #555; border-radius: 5px;">
                </div>
                
                <div style="text-align: center; margin: 15px 0;">
                    <button id="auto-start" style="background: #00ff00; color: #000; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; font-weight: bold;">🚀 START AUTO</button>
                    <button id="auto-stop" style="background: #ff0000; color: #fff; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; font-weight: bold;">⏹️ STOP</button>
                </div>
                
                <div style="background: #222; padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                    <div style="display: flex; justify-content: space-between;">
                        <span>✅ Success: <span id="auto-success">0</span></span>
                        <span>❌ Failed: <span id="auto-failed">0</span></span>
                        <span>📊 Total: <span id="auto-total">0</span></span>
                    </div>
                    <div style="margin-top: 5px;">
                        🔄 Retry: <span id="auto-retry">0</span> | 
                        ⏱️ Status: <span id="auto-status">Ready</span>
                    </div>
                </div>
                
                <div style="background: #111; padding: 8px; border-radius: 5px; max-height: 100px; overflow-y: auto; font-size: 10px;" id="auto-log">
                    <div>📝 Logs will appear here...</div>
                </div>
                
                <div style="text-align: center; margin-top: 10px;">
                    <button id="manual-post" style="background: #ffaa00; color: #000; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; font-size: 10px;">📤 MANUAL POST</button>
                    <button id="kill-countdown" style="background: #ff4444; color: #fff; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; font-size: 10px;">💀 KILL TIMER</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
        setupEventListeners();
    }
    
    function setupEventListeners() {
        document.getElementById('auto-start').onclick = startAutoPost;
        document.getElementById('auto-stop').onclick = stopAutoPost;
        document.getElementById('manual-post').onclick = manualPost;
        document.getElementById('kill-countdown').onclick = killCountdown;
    }
    
    // ========================================
    // 📝 LOGGING & UI UPDATES
    // ========================================
    function log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logElement = document.getElementById('auto-log');
        
        const logEntry = document.createElement('div');
        logEntry.innerHTML = `[${timestamp}] ${message}`;
        logEntry.style.color = type === 'success' ? '#00ff00' : type === 'error' ? '#ff0000' : '#ffffff';
        
        logElement.appendChild(logEntry);
        logElement.scrollTop = logElement.scrollHeight;
        
        // Keep only last 20 logs
        while (logElement.children.length > 20) {
            logElement.removeChild(logElement.firstChild);
        }
        
        console.log(`🚀 AutoPost: ${message}`);
    }
    
    function updateStatus(status) {
        document.getElementById('auto-status').textContent = status;
    }
    
    function updateStats() {
        document.getElementById('auto-success').textContent = stats.success;
        document.getElementById('auto-failed').textContent = stats.failed;
        document.getElementById('auto-total').textContent = stats.total;
        document.getElementById('auto-retry').textContent = retryCount;
    }
    
    // ========================================
    // 💀 COUNTDOWN KILLER
    // ========================================
    function killCountdown() {
        log('💀 Killing countdown...', 'info');
        
        // Kill interval
        if (window.cdtm) {
            clearInterval(window.cdtm);
            log('✅ Killed cdtm interval', 'success');
        }
        
        // Kill all intervals
        for (let i = 0; i < 99999; i++) {
            clearInterval(i);
            clearTimeout(i);
        }
        
        // Update countdown display
        const countdownEl = document.querySelector('.views-countdown');
        if (countdownEl) {
            countdownEl.innerHTML = '⚡ COUNTDOWN KILLED - READY TO POST! ⚡';
            countdownEl.style.color = '#00ff00';
            countdownEl.style.fontWeight = 'bold';
        }
        
        // Enable buttons
        document.querySelectorAll('button, input[type="submit"]').forEach(btn => {
            btn.disabled = false;
            btn.classList.remove('disableButton');
        });
        
        log('✅ Countdown killed successfully!', 'success');
    }
    
    // ========================================
    // 📤 POST FUNCTIONS
    // ========================================
    async function sendPostRequest(videoUrl) {
        try {
            log(`📤 Sending POST to: ${CONFIG.POST_URL}`, 'info');
            
            // Prepare form data
            const formData = new FormData();
            formData.append(CONFIG.FORM_NAME, videoUrl);
            
            // Get current cookies and headers
            const headers = {
                'Accept': '*/*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Cache-Control': 'no-cache',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://zefoy.com',
                'Referer': 'https://zefoy.com/',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': navigator.userAgent,
                'X-Requested-With': 'XMLHttpRequest'
            };
            
            // Convert FormData to URLSearchParams for proper encoding
            const params = new URLSearchParams();
            params.append(CONFIG.FORM_NAME, videoUrl);
            
            const response = await fetch(CONFIG.POST_URL, {
                method: 'POST',
                headers: headers,
                body: params,
                credentials: 'same-origin'
            });
            
            log(`📨 Response status: ${response.status}`, 'info');
            
            if (response.ok) {
                const responseText = await response.text();
                log(`📥 Response received (${responseText.length} chars)`, 'success');
                
                // Try to decode base64 response (Zefoy style)
                const decodedResponse = decodeZefoyResponse(responseText);
                
                return {
                    success: true,
                    data: decodedResponse || responseText,
                    status: response.status
                };
            } else {
                log(`❌ HTTP Error: ${response.status}`, 'error');
                return {
                    success: false,
                    error: `HTTP ${response.status}`,
                    status: response.status
                };
            }
            
        } catch (error) {
            log(`❌ Network error: ${error.message}`, 'error');
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    function decodeZefoyResponse(responseText) {
        try {
            // Zefoy thường encode response bằng base64 reverse
            const reversed = responseText.split('').reverse().join('');
            const decoded = atob(decodeURIComponent(reversed));
            log('🔓 Decoded Zefoy response', 'info');
            return decoded;
        } catch (e) {
            // Nếu không decode được thì return original
            return responseText;
        }
    }
    
    function analyzeResponse(responseData) {
        const text = responseData.toLowerCase();
        
        if (text.includes('successfully') && text.includes('views sent')) {
            const viewsMatch = text.match(/successfully (\d+) views sent/);
            const views = viewsMatch ? viewsMatch[1] : 'unknown';
            return {
                success: true,
                message: `✅ Successfully sent ${views} views!`,
                waitTime: 30000 // 30 seconds
            };
        } else if (text.includes('please wait')) {
            const waitMatch = text.match(/wait (\d+) seconds/);
            const waitTime = waitMatch ? parseInt(waitMatch[1]) * 1000 : 60000;
            return {
                success: false,
                message: `⏳ Need to wait ${Math.round(waitTime/1000)} seconds`,
                waitTime: waitTime
            };
        } else if (text.includes('too many requests')) {
            return {
                success: false,
                message: '🚫 Too many requests - need to wait',
                waitTime: 120000 // 2 minutes
            };
        } else if (text.includes('service is currently not working')) {
            return {
                success: false,
                message: '❌ Service not working',
                waitTime: 300000 // 5 minutes
            };
        } else {
            return {
                success: false,
                message: '❓ Unknown response',
                waitTime: 60000 // 1 minute
            };
        }
    }
    
    // ========================================
    // 🔄 AUTO POST LOGIC
    // ========================================
    async function startAutoPost() {
        const videoUrl = document.getElementById('auto-video-url').value.trim();
        
        if (!videoUrl || !videoUrl.includes('tiktok.com')) {
            alert('❌ Please enter a valid TikTok URL!');
            return;
        }
        
        CONFIG.VIDEO_URL = videoUrl;
        isRunning = true;
        retryCount = 0;
        
        log('🚀 Starting auto post...', 'success');
        updateStatus('Running');
        
        await autoPostLoop();
    }
    
    function stopAutoPost() {
        isRunning = false;
        log('⏹️ Auto post stopped', 'info');
        updateStatus('Stopped');
    }
    
    async function autoPostLoop() {
        while (isRunning) {
            try {
                stats.total++;
                updateStats();
                
                log(`🔄 Attempt ${stats.total}: Sending views...`, 'info');
                updateStatus('Sending...');
                
                // Kill countdown before each attempt
                killCountdown();
                
                // Send POST request
                const result = await sendPostRequest(CONFIG.VIDEO_URL);
                
                if (result.success) {
                    const analysis = analyzeResponse(result.data);
                    
                    if (analysis.success) {
                        stats.success++;
                        log(analysis.message, 'success');
                        updateStatus('Success!');
                    } else {
                        stats.failed++;
                        log(analysis.message, 'error');
                        updateStatus('Failed');
                    }
                    
                    updateStats();
                    
                    // Wait before next attempt
                    const waitTime = analysis.waitTime || CONFIG.RETRY_DELAY;
                    log(`⏳ Waiting ${Math.round(waitTime/1000)} seconds...`, 'info');
                    updateStatus(`Waiting ${Math.round(waitTime/1000)}s`);
                    
                    await delay(waitTime);
                    
                } else {
                    stats.failed++;
                    retryCount++;
                    log(`❌ Request failed: ${result.error}`, 'error');
                    updateStatus('Request Failed');
                    updateStats();
                    
                    await delay(CONFIG.RETRY_DELAY);
                }
                
            } catch (error) {
                log(`❌ Loop error: ${error.message}`, 'error');
                await delay(CONFIG.RETRY_DELAY);
            }
        }
    }
    
    async function manualPost() {
        const videoUrl = document.getElementById('auto-video-url').value.trim();
        
        if (!videoUrl) {
            alert('❌ Please enter a TikTok URL first!');
            return;
        }
        
        log('📤 Manual POST triggered...', 'info');
        killCountdown();
        
        const result = await sendPostRequest(videoUrl);
        
        if (result.success) {
            const analysis = analyzeResponse(result.data);
            log(analysis.message, analysis.success ? 'success' : 'error');
        } else {
            log(`❌ Manual POST failed: ${result.error}`, 'error');
        }
    }
    
    function delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // ========================================
    // 🚀 INITIALIZE
    // ========================================
    
    // Create UI
    createAutoPostUI();
    
    // Global commands
    window.autoPost = startAutoPost;
    window.stopPost = stopAutoPost;
    window.manualPost = manualPost;
    window.killTimer = killCountdown;
    
    // Auto-fill URL if found in page
    setTimeout(() => {
        const existingInput = document.querySelector('input[placeholder*="Enter Video URL"]');
        if (existingInput && existingInput.value) {
            document.getElementById('auto-video-url').value = existingInput.value;
            log('📝 Auto-filled URL from page', 'info');
        }
    }, 1000);
    
    log('✅ Auto Post Sender loaded!', 'success');
    
    console.log(`
    🚀 ZEFOY AUTO POST SENDER LOADED!
    
    📤 Features:
    - Auto POST to: ${CONFIG.POST_URL}
    - Countdown killer
    - Response analysis
    - Auto retry logic
    - Real-time stats
    
    🎮 Commands:
    - autoPost()   : Start auto posting
    - stopPost()   : Stop auto posting
    - manualPost() : Send single POST
    - killTimer()  : Kill countdown
    
    💡 Panel appeared on top-right!
    `);
    
})();
