// ========================================
// ⚡ ZEFOY TIME HACKER - SIÊU NHANH
// ========================================
// Hack thời gian countdown để không phải đợi!

(function() {
    'use strict';
    
    console.log('⚡ ZEFOY TIME HACKER LOADING...');
    
    // ========================================
    // 🕒 TIME MANIPULATION
    // ========================================
    
    // Override Date.now() để làm thời gian chạy nhanh hơn
    const originalDateNow = Date.now;
    const originalGetTime = Date.prototype.getTime;
    
    let timeMultiplier = 100; // Thời gian chạy nhanh gấp 100 lần!
    let baseTime = originalDateNow();
    
    // Hack Date.now()
    Date.now = function() {
        const elapsed = originalDateNow() - baseTime;
        return baseTime + (elapsed * timeMultiplier);
    };
    
    // Hack Date.prototype.getTime()
    Date.prototype.getTime = function() {
        const elapsed = originalDateNow() - baseTime;
        return baseTime + (elapsed * timeMultiplier);
    };
    
    // ========================================
    // 🎯 COUNTDOWN KILLER
    // ========================================
    
    function killCountdown() {
        console.log('🎯 Killing countdown timers...');
        
        // Clear tất cả intervals
        for (let i = 1; i < 99999; i++) {
            window.clearInterval(i);
        }
        
        // Clear tất cả timeouts
        for (let i = 1; i < 99999; i++) {
            window.clearTimeout(i);
        }
        
        // Tìm và kill countdown cụ thể
        if (window.cdtm) {
            clearInterval(window.cdtm);
            console.log('✅ Killed cdtm interval');
        }
        
        // Override setInterval để block countdown
        const originalSetInterval = window.setInterval;
        window.setInterval = function(callback, delay) {
            const callbackStr = callback.toString();
            
            // Block countdown intervals
            if (callbackStr.includes('countdown') || 
                callbackStr.includes('Please wait') ||
                callbackStr.includes('minute') ||
                callbackStr.includes('fark') ||
                callbackStr.includes('stm') ||
                callbackStr.includes('ltm')) {
                console.log('🚫 Blocked countdown interval!');
                return 999999; // Return fake interval ID
            }
            
            return originalSetInterval(callback, delay);
        };
        
        console.log('✅ Countdown killer activated!');
    }
    
    // ========================================
    // 🚀 INSTANT READY
    // ========================================
    
    function makeInstantReady() {
        console.log('🚀 Making submit instantly ready...');
        
        // Tìm và update countdown text
        const countdownElements = document.querySelectorAll('.views-countdown, .br');
        countdownElements.forEach(el => {
            if (el.textContent.includes('Please wait') || el.textContent.includes('minute')) {
                el.innerHTML = '<span style="color: #00ff00; font-weight: bold;">⚡ READY NOW - HACKED! ⚡</span>';
                el.style.color = '#00ff00';
                console.log('✅ Updated countdown text');
            }
        });
        
        // Enable tất cả buttons
        const buttons = document.querySelectorAll('button, input[type="submit"]');
        buttons.forEach(btn => {
            btn.disabled = false;
            btn.classList.remove('disableButton');
            btn.style.opacity = '1';
            btn.style.pointerEvents = 'auto';
        });
        
        // Remove disabled classes
        document.querySelectorAll('.disableButton').forEach(el => {
            el.classList.remove('disableButton');
        });
        
        console.log('✅ All buttons enabled!');
    }
    
    // ========================================
    // 🎨 SPEED UI
    // ========================================
    
    function createSpeedUI() {
        const ui = document.createElement('div');
        ui.innerHTML = `
            <div style="
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(45deg, #ff0000, #00ff00);
                color: #fff;
                padding: 20px;
                border-radius: 15px;
                font-family: monospace;
                font-size: 14px;
                z-index: 999999;
                text-align: center;
                box-shadow: 0 0 30px rgba(255, 0, 0, 0.8);
                border: 3px solid #fff;
                animation: pulse 1s infinite;
            ">
                <div style="font-size: 18px; font-weight: bold; margin-bottom: 10px;">
                    ⚡ TIME HACKER ACTIVATED! ⚡
                </div>
                
                <div style="margin-bottom: 15px;">
                    🕒 Time Speed: <span id="time-speed">${timeMultiplier}x</span><br>
                    🎯 Countdown: <span style="color: #00ff00;">KILLED</span><br>
                    🚀 Status: <span style="color: #00ff00;">READY TO SUBMIT</span>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <button onclick="changeTimeSpeed(50)" style="background: #ff4444; color: #fff; border: none; padding: 5px 10px; margin: 2px; border-radius: 5px; cursor: pointer;">50x</button>
                    <button onclick="changeTimeSpeed(100)" style="background: #ff8800; color: #fff; border: none; padding: 5px 10px; margin: 2px; border-radius: 5px; cursor: pointer;">100x</button>
                    <button onclick="changeTimeSpeed(500)" style="background: #ffff00; color: #000; border: none; padding: 5px 10px; margin: 2px; border-radius: 5px; cursor: pointer;">500x</button>
                    <button onclick="changeTimeSpeed(1000)" style="background: #00ff00; color: #000; border: none; padding: 5px 10px; margin: 2px; border-radius: 5px; cursor: pointer;">1000x</button>
                </div>
                
                <div style="font-size: 12px; color: #ffff00;">
                    💡 Countdown đã bị hack! Có thể submit ngay!
                </div>
                
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="position: absolute; top: 5px; right: 10px; background: #ff0000; color: #fff; border: none; border-radius: 50%; width: 25px; height: 25px; cursor: pointer;">×</button>
            </div>
            
            <style>
                @keyframes pulse {
                    0% { transform: translate(-50%, -50%) scale(1); }
                    50% { transform: translate(-50%, -50%) scale(1.05); }
                    100% { transform: translate(-50%, -50%) scale(1); }
                }
            </style>
        `;
        
        document.body.appendChild(ui);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (ui.parentElement) {
                ui.remove();
            }
        }, 5000);
    }
    
    // ========================================
    // 🎮 CONTROL FUNCTIONS
    // ========================================
    
    window.changeTimeSpeed = function(multiplier) {
        timeMultiplier = multiplier;
        baseTime = originalDateNow(); // Reset base time
        document.getElementById('time-speed').textContent = multiplier + 'x';
        console.log(`⚡ Time speed changed to ${multiplier}x`);
        
        // Re-kill countdown with new speed
        setTimeout(killCountdown, 100);
        setTimeout(makeInstantReady, 200);
    };
    
    window.instantSubmit = function() {
        makeInstantReady();
        
        // Auto-click submit button
        const submitBtn = document.querySelector('button[type="submit"], input[type="submit"]');
        if (submitBtn) {
            submitBtn.click();
            console.log('🚀 Auto-clicked submit!');
        }
    };
    
    window.killAllTimers = function() {
        killCountdown();
        makeInstantReady();
        console.log('💀 All timers killed!');
    };
    
    // ========================================
    // 🚀 AUTO EXECUTION
    // ========================================
    
    function autoHack() {
        console.log('🚀 Starting auto hack sequence...');
        
        // Step 1: Kill countdown
        setTimeout(killCountdown, 100);
        
        // Step 2: Make ready
        setTimeout(makeInstantReady, 500);
        
        // Step 3: Show UI
        setTimeout(createSpeedUI, 1000);
        
        // Step 4: Continuous monitoring
        setInterval(() => {
            makeInstantReady();
        }, 2000);
        
        console.log('✅ Auto hack sequence completed!');
    }
    
    // ========================================
    // 🎯 ADVANCED HACKS
    // ========================================
    
    // Override Math.floor để làm countdown về 0
    const originalMathFloor = Math.floor;
    Math.floor = function(x) {
        // Nếu đang tính toán countdown, return 0
        if (typeof x === 'number' && x > 1000000000) { // Unix timestamp
            return 0;
        }
        return originalMathFloor(x);
    };
    
    // Override parseInt cho countdown
    const originalParseInt = parseInt;
    window.parseInt = function(string, radix) {
        const result = originalParseInt(string, radix);
        
        // Nếu là phần tính toán countdown, return 0
        if (typeof string === 'number' && string < 0) {
            return 0;
        }
        
        return result;
    };
    
    // ========================================
    // 🚀 INSTANT COMMANDS
    // ========================================
    
    // Command để hack ngay lập tức
    window.hackNow = function() {
        console.log('💥 HACK NOW ACTIVATED!');
        
        // Kill tất cả
        killCountdown();
        makeInstantReady();
        
        // Force update countdown
        const countdownEl = document.querySelector('.views-countdown');
        if (countdownEl) {
            countdownEl.innerHTML = '⚡ HACKED - SUBMIT NOW! ⚡';
            countdownEl.style.color = '#00ff00';
            countdownEl.style.fontWeight = 'bold';
            countdownEl.style.fontSize = '16px';
            countdownEl.style.textShadow = '0 0 10px #00ff00';
        }
        
        // Enable submit button
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.classList.remove('disableButton');
            submitBtn.style.background = '#00ff00';
            submitBtn.style.color = '#000';
            submitBtn.innerHTML = '⚡ SUBMIT NOW - HACKED! ⚡';
        }
        
        alert('💥 COUNTDOWN HACKED! BẠN CÓ THỂ SUBMIT NGAY BÂY GIỜ!');
    };
    
    // ========================================
    // 🚀 INITIALIZE
    // ========================================
    
    // Wait for page load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', autoHack);
    } else {
        autoHack();
    }
    
    // Global commands
    window.speedHack = autoHack;
    window.timeHack = () => changeTimeSpeed(1000);
    window.noWait = makeInstantReady;
    
    console.log(`
    ⚡ ZEFOY TIME HACKER LOADED!
    
    🚀 INSTANT COMMANDS:
    - hackNow()     : Hack countdown ngay lập tức
    - timeHack()    : Tăng tốc thời gian 1000x
    - noWait()      : Bỏ qua thời gian chờ
    - instantSubmit(): Auto submit
    - killAllTimers(): Kill tất cả timers
    
    💥 COUNTDOWN ĐÃ BỊ HACK! SUBMIT NGAY ĐI!
    `);
    
    // Show notification
    setTimeout(() => {
        if (confirm('💥 TIME HACKER ACTIVATED!\n\nCountdown đã bị hack!\nBạn có muốn submit ngay không?')) {
            instantSubmit();
        }
    }, 2000);
    
})();
