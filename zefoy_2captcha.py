#!/usr/bin/env python3
"""
Zefoy <PERSON> với 2captcha API - Tự động giải captcha
"""

import requests
import time
import base64
import urllib.parse
from rich import print as printf
from rich.panel import Panel
from rich.console import Console

class Zefoy2Captcha:
    def __init__(self, api_key=None):
        self.api_key = api_key  # 2captcha API key
        self.session = requests.Session()
        self.cookies = {}
        
        # Headers giống browser thật
        self.headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Host': 'zefoy.com',
            'Sec-Ch-Ua': '"Google Chrome";v="134", "Chromium";v="134", "Not-A.Brand";v="99"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
    
    def solve_captcha_2captcha(self, captcha_image_path):
        """Giải captcha bằng 2captcha API"""
        if not self.api_key:
            printf("[bold red]❌ Chưa có API key 2captcha!")
            return None
        
        try:
            printf("[bold yellow]🤖 Đang gửi captcha đến 2captcha...")
            
            # Đọc file captcha
            with open(captcha_image_path, 'rb') as f:
                captcha_data = base64.b64encode(f.read()).decode()
            
            # Gửi captcha đến 2captcha
            submit_url = "http://2captcha.com/in.php"
            submit_data = {
                'method': 'base64',
                'key': self.api_key,
                'body': captcha_data
            }
            
            response = requests.post(submit_url, data=submit_data)
            
            if response.text.startswith('OK|'):
                captcha_id = response.text.split('|')[1]
                printf(f"[bold green]✅ Captcha đã gửi, ID: {captcha_id}")
                
                # Đợi kết quả
                printf("[bold yellow]⏳ Đang đợi kết quả...")
                
                for attempt in range(30):  # Đợi tối đa 5 phút
                    time.sleep(10)
                    
                    result_url = f"http://2captcha.com/res.php?key={self.api_key}&action=get&id={captcha_id}"
                    result = requests.get(result_url)
                    
                    if result.text == 'CAPCHA_NOT_READY':
                        printf(f"[bold yellow]⏳ Đợi... {attempt + 1}/30")
                        continue
                    elif result.text.startswith('OK|'):
                        captcha_text = result.text.split('|')[1]
                        printf(f"[bold green]✅ Captcha đã giải: {captcha_text}")
                        return captcha_text
                    else:
                        printf(f"[bold red]❌ Lỗi 2captcha: {result.text}")
                        return None
                
                printf("[bold red]❌ Timeout đợi kết quả 2captcha!")
                return None
            else:
                printf(f"[bold red]❌ Lỗi gửi captcha: {response.text}")
                return None
                
        except Exception as e:
            printf(f"[bold red]❌ Lỗi 2captcha: {e}")
            return None
    
    def get_captcha_manual(self, captcha_image_path):
        """Nhập captcha thủ công"""
        printf(f"[bold yellow]📷 Captcha đã lưu tại: {captcha_image_path}")
        printf("[bold cyan]👀 Hãy mở file để xem captcha!")
        
        console = Console()
        captcha_text = console.input("[bold green]✏️  Nhập captcha: ")
        return captcha_text.strip()
    
    def login(self, use_2captcha=False):
        """Đăng nhập vào Zefoy"""
        try:
            printf("[bold yellow]🔑 Đang đăng nhập...")
            
            # Lấy trang chính
            response = self.session.get('https://zefoy.com/', headers=self.headers)
            
            if response.status_code == 403:
                printf("[bold red]❌ Bị chặn bởi Cloudflare!")
                return False
            
            # Tìm captcha
            import re
            captcha_match = re.search(r'src="(.*?)" onerror="errimg\(\)"', response.text)
            form_match = re.search(r'type="text" id="captchatoken" name="(.*?)"', response.text)
            
            if not captcha_match or not form_match:
                printf("[bold red]❌ Không tìm thấy captcha!")
                return False
            
            captcha_url = captcha_match.group(1).replace('amp;', '')
            form_name = form_match.group(1)
            
            # Tải captcha
            captcha_response = self.session.get(f'https://zefoy.com{captcha_url}', headers=self.headers)
            
            captcha_path = 'Penyimpanan/Gambar.png'
            with open(captcha_path, 'wb') as f:
                f.write(captcha_response.content)
            
            # Giải captcha
            if use_2captcha and self.api_key:
                captcha_text = self.solve_captcha_2captcha(captcha_path)
            else:
                captcha_text = self.get_captcha_manual(captcha_path)
            
            if not captcha_text:
                return False
            
            # Gửi form đăng nhập
            login_data = {form_name: captcha_text}
            login_headers = self.headers.copy()
            login_headers.update({
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://zefoy.com',
                'Referer': 'https://zefoy.com/'
            })
            
            login_response = self.session.post('https://zefoy.com/', data=login_data, headers=login_headers)
            
            if 'Enter Video URL' in login_response.text:
                printf("[bold green]✅ Đăng nhập thành công!")
                return True
            else:
                printf("[bold red]❌ Đăng nhập thất bại!")
                return False
                
        except Exception as e:
            printf(f"[bold red]❌ Lỗi đăng nhập: {e}")
            return False
    
    def send_views(self, video_url):
        """Gửi views cho video"""
        try:
            printf(f"[bold yellow]📹 Đang gửi views...")
            
            # Lấy form video
            response = self.session.get('https://zefoy.com/', headers=self.headers)
            
            import re
            video_form_match = re.search(r'name="(.*?)" placeholder="Enter Video URL"', response.text)
            action_matches = re.findall(r'action="(.*?)"', response.text)
            
            if not video_form_match or len(action_matches) < 4:
                printf("[bold red]❌ Không tìm thấy form video!")
                return False
            
            video_form = video_form_match.group(1)
            post_action = action_matches[3]
            
            # Gửi video URL
            video_data = {video_form: video_url}
            video_headers = self.headers.copy()
            video_headers.update({
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://zefoy.com',
                'Referer': 'https://zefoy.com/'
            })
            
            video_response = self.session.post(f'https://zefoy.com/{post_action}', 
                                             data=video_data, headers=video_headers)
            
            # Decode response
            decoded = self.decode_response(video_response.text)
            
            if 'Successfully' in decoded and 'views sent' in decoded:
                views_match = re.search(r'Successfully (.*?) views sent', decoded)
                views = views_match.group(1) if views_match else "unknown"
                printf(f"[bold green]✅ Gửi thành công {views} views!")
                return True
            elif 'Please wait' in decoded:
                wait_match = re.search(r'Please wait (.*?) seconds', decoded)
                wait_time = wait_match.group(1) if wait_match else "unknown"
                printf(f"[bold yellow]⏳ Cần đợi {wait_time} giây...")
                return False
            else:
                printf("[bold red]❌ Không thể gửi views!")
                return False
                
        except Exception as e:
            printf(f"[bold red]❌ Lỗi gửi views: {e}")
            return False
    
    def decode_response(self, encoded_text):
        """Decode response từ Zefoy"""
        try:
            reversed_text = encoded_text[::-1]
            decoded = base64.b64decode(urllib.parse.unquote(reversed_text)).decode()
            return decoded
        except:
            return encoded_text
    
    def run(self):
        """Chạy bot chính"""
        printf(Panel("[bold cyan]ZEFOY 2CAPTCHA BOT\n\nTự động giải captcha bằng AI", 
                     width=60, style="bold cyan", title="[bold cyan][ 2CAPTCHA BOT ]"))
        
        console = Console()
        
        # Hỏi API key
        use_2captcha = console.input("[bold yellow]Có sử dụng 2captcha API không? (y/n): ").lower() == 'y'
        
        if use_2captcha:
            api_key = console.input("[bold green]Nhập 2captcha API key: ").strip()
            if api_key:
                self.api_key = api_key
                printf("[bold green]✅ Đã thiết lập 2captcha API!")
            else:
                printf("[bold yellow]⚠️  Sẽ sử dụng captcha thủ công!")
                use_2captcha = False
        
        # Đăng nhập
        login_attempts = 0
        while login_attempts < 5:
            login_attempts += 1
            printf(f"[bold yellow]🔑 Thử đăng nhập lần {login_attempts}/5...")
            
            if self.login(use_2captcha):
                break
            
            if login_attempts < 5:
                time.sleep(3)
        else:
            printf("[bold red]❌ Không thể đăng nhập sau 5 lần thử!")
            return
        
        # Nhập video URL
        video_url = console.input("[bold green]📹 Nhập link video TikTok: ")
        
        if not ('tiktok.com' in video_url and '/video/' in video_url):
            printf("[bold red]❌ Link TikTok không hợp lệ!")
            return
        
        # Gửi views liên tục
        success_count = 0
        fail_count = 0
        
        printf("[bold green]🚀 Bắt đầu gửi views...")
        
        while True:
            try:
                if self.send_views(video_url):
                    success_count += 1
                    printf(f"[bold green]📊 Thành công: {success_count} | Thất bại: {fail_count}")
                    time.sleep(30)  # Đợi 30 giây
                else:
                    fail_count += 1
                    printf(f"[bold red]📊 Thành công: {success_count} | Thất bại: {fail_count}")
                    time.sleep(60)  # Đợi 60 giây khi fail
                
            except KeyboardInterrupt:
                printf("\n[bold yellow]⏹️  Dừng bot!")
                break
            except Exception as e:
                printf(f"[bold red]❌ Lỗi: {e}")
                time.sleep(30)

if __name__ == "__main__":
    bot = Zefoy2Captcha()
    bot.run()
