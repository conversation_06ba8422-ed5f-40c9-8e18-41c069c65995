// ========================================
// 💥 COUNTDOWN KILLER - 1 DÒNG LỆNH
// ========================================
// Paste vào console để kill countdown ngay lập tức!

// 🎯 KILL COUNTDOWN NGAY LẬP TỨC
(function(){
    console.log('💥 COUNTDOWN KILLER ACTIVATED!');
    
    // Kill interval countdown
    if(window.cdtm) clearInterval(window.cdtm);
    for(let i=0;i<99999;i++) clearInterval(i);
    
    // Update countdown text
    document.querySelectorAll('.views-countdown, .br').forEach(el => {
        if(el.textContent.includes('Please wait') || el.textContent.includes('minute')) {
            el.innerHTML = '⚡ READY NOW - NO WAIT! ⚡';
            el.style.color = '#00ff00';
            el.style.fontWeight = 'bold';
            el.style.fontSize = '16px';
        }
    });
    
    // Enable all buttons
    document.querySelectorAll('button, input[type="submit"]').forEach(btn => {
        btn.disabled = false;
        btn.classList.remove('disableButton');
        btn.style.opacity = '1';
        btn.style.background = '#00ff00';
        btn.style.color = '#000';
    });
    
    // Override setInterval to block new countdowns
    const origSetInterval = setInterval;
    window.setInterval = function(fn, delay) {
        if(fn.toString().includes('countdown') || fn.toString().includes('Please wait')) {
            console.log('🚫 Blocked new countdown!');
            return 999999;
        }
        return origSetInterval(fn, delay);
    };
    
    alert('💥 COUNTDOWN KILLED! SUBMIT NGAY ĐI!');
    console.log('✅ Ready to submit immediately!');
})();

// ========================================
// 🚀 QUICK COMMANDS
// ========================================

// Command 1: Kill countdown
window.killWait = () => {
    if(window.cdtm) clearInterval(window.cdtm);
    document.querySelector('.views-countdown').innerHTML = '⚡ NO WAIT - READY! ⚡';
    document.querySelector('button[type="submit"]').disabled = false;
    console.log('💥 Wait time killed!');
};

// Command 2: Instant submit
window.submitNow = () => {
    killWait();
    document.querySelector('button[type="submit"]').click();
    console.log('🚀 Submitted instantly!');
};

// Command 3: Speed hack
window.speedUp = () => {
    // Override Date.now for 100x speed
    const orig = Date.now;
    Date.now = () => orig() * 100;
    killWait();
    console.log('⚡ Time speed hacked!');
};

console.log(`
💥 COUNTDOWN KILLER LOADED!

🚀 QUICK COMMANDS:
- killWait()   : Kill countdown
- submitNow()  : Submit immediately  
- speedUp()    : 100x time speed

💡 Countdown đã bị kill! Submit ngay!
`);
