// ========================================
// ⚡ 1-LINER COMMANDS - SIÊU NHANH
// ========================================
// Copy từng dòng và paste vào console

// 💥 KILL COUNTDOWN (1 dòng)
clearInterval(window.cdtm);for(let i=0;i<99999;i++)clearInterval(i);document.querySelectorAll('.views-countdown,.br').forEach(e=>e.innerHTML='⚡READY⚡');document.querySelectorAll('button,input[type="submit"]').forEach(b=>{b.disabled=false;b.classList.remove('disableButton')});alert('💥 COUNTDOWN KILLED!');

// 🚀 INSTANT SUBMIT (1 dòng)  
clearInterval(window.cdtm);document.querySelector('.views-countdown').innerHTML='⚡READY⚡';document.querySelector('button[type="submit"]').disabled=false;document.querySelector('button[type="submit"]').click();

// ⚡ SPEED HACK (1 dòng)
Date.now=()=>Date.now()*1000;clearInterval(window.cdtm);document.querySelector('.views-countdown').innerHTML='⚡HACKED⚡';document.querySelector('button[type="submit"]').disabled=false;

// 🎯 AUTO FILL + SUBMIT (1 dòng)
document.querySelector('input[name="1ebb17d8fdd93df9"]').value='YOUR_TIKTOK_URL';clearInterval(window.cdtm);document.querySelector('button[type="submit"]').disabled=false;document.querySelector('button[type="submit"]').click();

// 💀 NUCLEAR OPTION - KILL EVERYTHING (1 dòng)
for(let i=0;i<99999;i++){clearInterval(i);clearTimeout(i);}document.querySelectorAll('.views-countdown,.br').forEach(e=>e.innerHTML='⚡NUKED⚡');document.querySelectorAll('button,input').forEach(b=>{b.disabled=false;b.classList.remove('disableButton');b.style.background='#00ff00'});alert('💀 EVERYTHING KILLED!');

// ========================================
// 🎮 INTERACTIVE COMMANDS
// ========================================

// Command để tạo button hack nhanh
(function(){
    const btn = document.createElement('button');
    btn.innerHTML = '💥 HACK NOW';
    btn.style.cssText = 'position:fixed;top:10px;right:10px;z-index:999999;background:#ff0000;color:#fff;border:none;padding:15px;border-radius:10px;font-size:16px;cursor:pointer;box-shadow:0 0 20px #ff0000;';
    btn.onclick = () => {
        clearInterval(window.cdtm);
        document.querySelector('.views-countdown').innerHTML = '⚡ HACKED - SUBMIT NOW! ⚡';
        document.querySelector('button[type="submit"]').disabled = false;
        document.querySelector('button[type="submit"]').style.background = '#00ff00';
        alert('💥 HACKED! Submit ngay!');
    };
    document.body.appendChild(btn);
})();

// Command để auto-loop submit
window.autoLoop = () => {
    setInterval(() => {
        clearInterval(window.cdtm);
        document.querySelector('.views-countdown').innerHTML = '⚡ AUTO LOOP ⚡';
        const btn = document.querySelector('button[type="submit"]');
        if(btn) {
            btn.disabled = false;
            btn.click();
        }
    }, 5000); // Submit mỗi 5 giây
    console.log('🔄 Auto loop started!');
};

// Command để hack time server
window.hackTime = () => {
    // Override tất cả time functions
    const now = Date.now();
    Date.now = () => now + 999999999; // Jump to future
    Date.prototype.getTime = () => now + 999999999;
    Math.floor = (x) => x > 1000000000 ? 0 : Math.floor(x);
    
    clearInterval(window.cdtm);
    document.querySelector('.views-countdown').innerHTML = '⚡ TIME HACKED ⚡';
    console.log('🕒 Time hacked to future!');
};

console.log(`
⚡ 1-LINER COMMANDS LOADED!

💥 COPY & PASTE THESE:

1. Kill countdown:
clearInterval(window.cdtm);document.querySelector('.views-countdown').innerHTML='⚡READY⚡';document.querySelector('button[type="submit"]').disabled=false;

2. Instant submit:
clearInterval(window.cdtm);document.querySelector('button[type="submit"]').disabled=false;document.querySelector('button[type="submit"]').click();

3. Auto loop:
autoLoop();

4. Hack time:
hackTime();

💡 Chỉ cần copy 1 dòng và paste!
`);
