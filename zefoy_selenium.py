#!/usr/bin/env python3
"""
Zefoy <PERSON> với Selenium - Bypass Cloudflare
"""

import time
import random
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from rich import print as printf
from rich.panel import Panel
from rich.console import Console

class ZefoySelenium:
    def __init__(self):
        self.driver = None
        self.setup_driver()
    
    def setup_driver(self):
        """Thiết lập Chrome driver với stealth mode"""
        try:
            printf("[bold yellow]🚀 Đang khởi động Chrome driver...")
            
            options = uc.ChromeOptions()
            
            # Stealth options
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Fake user agent
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
            
            # Disable images để tăng tốc
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2
            }
            options.add_experimental_option("prefs", prefs)
            
            self.driver = uc.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            printf("[bold green]✅ Chrome driver đã sẵn sàng!")
            return True
            
        except Exception as e:
            printf(f"[bold red]❌ Lỗi khởi động driver: {e}")
            return False
    
    def bypass_cloudflare(self):
        """Bypass Cloudflare protection"""
        try:
            printf("[bold yellow]☁️  Đang bypass Cloudflare...")
            
            self.driver.get("https://zefoy.com")
            
            # Đợi page load
            time.sleep(5)
            
            # Kiểm tra Cloudflare challenge
            if "Just a moment" in self.driver.page_source or "Checking your browser" in self.driver.page_source:
                printf("[bold yellow]⏳ Đang chờ Cloudflare challenge...")
                
                # Đợi tối đa 30 giây cho Cloudflare
                wait_time = 0
                while wait_time < 30:
                    time.sleep(1)
                    wait_time += 1
                    
                    if "Enter Video URL" in self.driver.page_source:
                        printf("[bold green]✅ Đã bypass Cloudflare thành công!")
                        return True
                    
                    printf(f"[bold yellow]⏳ Đợi Cloudflare... {wait_time}/30s", end='\r')
                
                printf("[bold red]❌ Timeout bypass Cloudflare!")
                return False
            
            elif "Enter Video URL" in self.driver.page_source:
                printf("[bold green]✅ Không có Cloudflare challenge!")
                return True
            
            else:
                printf("[bold red]❌ Trang không load đúng!")
                return False
                
        except Exception as e:
            printf(f"[bold red]❌ Lỗi bypass Cloudflare: {e}")
            return False
    
    def solve_captcha_manual(self):
        """Giải captcha thủ công"""
        try:
            printf("[bold yellow]🔍 Đang tìm captcha...")
            
            # Tìm captcha image
            captcha_img = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//img[contains(@src, 'captcha')]"))
            )
            
            printf("[bold green]📷 Đã tìm thấy captcha!")
            printf("[bold cyan]👀 Hãy nhìn vào browser và xem captcha!")
            
            # Tìm input captcha
            captcha_input = self.driver.find_element(By.ID, "captchatoken")
            
            # Cho user nhập captcha
            console = Console()
            captcha_text = console.input("[bold green]✏️  Nhập captcha từ browser: ")
            
            # Nhập captcha
            captcha_input.clear()
            captcha_input.send_keys(captcha_text)
            
            # Submit form
            submit_btn = self.driver.find_element(By.XPATH, "//input[@type='submit']")
            submit_btn.click()
            
            # Đợi kết quả
            time.sleep(3)
            
            if "Enter Video URL" in self.driver.page_source:
                printf("[bold green]✅ Đăng nhập thành công!")
                return True
            else:
                printf("[bold red]❌ Captcha sai, thử lại!")
                return False
                
        except TimeoutException:
            printf("[bold red]❌ Không tìm thấy captcha!")
            return False
        except Exception as e:
            printf(f"[bold red]❌ Lỗi giải captcha: {e}")
            return False
    
    def send_views(self, video_url):
        """Gửi views cho video"""
        try:
            printf(f"[bold yellow]📹 Đang gửi views cho: {video_url}")
            
            # Tìm input video URL
            video_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//input[@placeholder='Enter Video URL']"))
            )
            
            # Nhập URL
            video_input.clear()
            video_input.send_keys(video_url)
            
            # Tìm và click submit button
            submit_btn = self.driver.find_element(By.XPATH, "//input[@type='submit' and @value='Send']")
            submit_btn.click()
            
            # Đợi kết quả
            time.sleep(5)
            
            # Kiểm tra kết quả
            page_source = self.driver.page_source
            
            if "Successfully" in page_source and "views sent" in page_source:
                printf("[bold green]✅ Gửi views thành công!")
                return True
            elif "Please wait" in page_source:
                printf("[bold yellow]⏳ Cần đợi trước khi gửi tiếp...")
                return False
            elif "This service is currently not working" in page_source:
                printf("[bold red]❌ Dịch vụ hiện tại không hoạt động!")
                return False
            else:
                printf("[bold red]❌ Không thể gửi views!")
                return False
                
        except Exception as e:
            printf(f"[bold red]❌ Lỗi gửi views: {e}")
            return False
    
    def run(self):
        """Chạy bot chính"""
        try:
            printf(Panel("[bold cyan]ZEFOY SELENIUM BOT\n\nSử dụng browser thật để bypass Cloudflare", 
                         width=60, style="bold cyan", title="[bold cyan][ SELENIUM BOT ]"))
            
            # Bypass Cloudflare
            if not self.bypass_cloudflare():
                printf("[bold red]❌ Không thể bypass Cloudflare!")
                return
            
            # Đăng nhập với captcha
            login_success = False
            attempts = 0
            max_attempts = 5
            
            while not login_success and attempts < max_attempts:
                attempts += 1
                printf(f"[bold yellow]🔑 Thử đăng nhập lần {attempts}/{max_attempts}...")
                
                login_success = self.solve_captcha_manual()
                
                if not login_success:
                    time.sleep(2)
                    self.driver.refresh()
                    time.sleep(3)
            
            if not login_success:
                printf("[bold red]❌ Không thể đăng nhập sau 5 lần thử!")
                return
            
            # Nhập video URL
            console = Console()
            video_url = console.input("[bold green]📹 Nhập link video TikTok: ")
            
            if not ('tiktok.com' in video_url and '/video/' in video_url):
                printf("[bold red]❌ Link TikTok không hợp lệ!")
                return
            
            # Gửi views liên tục
            success_count = 0
            fail_count = 0
            
            printf("[bold green]🚀 Bắt đầu gửi views...")
            
            while True:
                try:
                    if self.send_views(video_url):
                        success_count += 1
                        printf(f"[bold green]✅ Thành công: {success_count} | Thất bại: {fail_count}")
                        
                        # Đợi random 30-60 giây
                        wait_time = random.randint(30, 60)
                        printf(f"[bold yellow]⏳ Đợi {wait_time} giây...")
                        time.sleep(wait_time)
                    else:
                        fail_count += 1
                        printf(f"[bold red]❌ Thành công: {success_count} | Thất bại: {fail_count}")
                        
                        # Đợi lâu hơn khi fail
                        wait_time = random.randint(60, 120)
                        printf(f"[bold yellow]⏳ Đợi {wait_time} giây...")
                        time.sleep(wait_time)
                        
                        # Refresh page nếu fail nhiều
                        if fail_count % 3 == 0:
                            printf("[bold yellow]🔄 Refresh trang...")
                            self.driver.refresh()
                            time.sleep(5)
                
                except KeyboardInterrupt:
                    printf("\n[bold yellow]⏹️  Dừng bot bởi user!")
                    break
                except Exception as e:
                    printf(f"[bold red]❌ Lỗi: {e}")
                    time.sleep(10)
                    continue
        
        finally:
            if self.driver:
                self.driver.quit()
                printf("[bold green]✅ Đã đóng browser!")

if __name__ == "__main__":
    bot = ZefoySelenium()
    bot.run()
